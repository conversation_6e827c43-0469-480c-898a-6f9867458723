<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Expense Tracker</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-wallet"></i> Expense Tracker</h1>
            <div class="header-actions">
                <button id="exportBtn" class="btn btn-secondary">
                    <i class="fas fa-download"></i> Export CSV
                </button>
                <button id="syncBtn" class="btn btn-secondary">
                    <i class="fas fa-cloud"></i> Sync
                </button>
            </div>
        </header>

        <div class="dashboard">
            <div class="summary-cards">
                <div class="card">
                    <h3>Total Balance</h3>
                    <p id="totalBalance" class="amount">$0.00</p>
                </div>
                <div class="card">
                    <h3>Total Income</h3>
                    <p id="totalIncome" class="amount income">$0.00</p>
                </div>
                <div class="card">
                    <h3>Total Expenses</h3>
                    <p id="totalExpenses" class="amount expense">$0.00</p>
                </div>
            </div>

            <div class="main-content">
                <div class="left-panel">
                    <div class="add-transaction">
                        <h2>Add Transaction</h2>
                        <form id="transactionForm">
                            <div class="form-group">
                                <label for="description">Description</label>
                                <input type="text" id="description" required>
                            </div>
                            <div class="form-group">
                                <label for="amount">Amount</label>
                                <input type="number" id="amount" step="0.01" required>
                            </div>
                            <div class="form-group">
                                <label for="type">Type</label>
                                <select id="type" required>
                                    <option value="expense">Expense</option>
                                    <option value="income">Income</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="category">Category</label>
                                <select id="category" required>
                                    <option value="food">Food & Dining</option>
                                    <option value="transport">Transportation</option>
                                    <option value="entertainment">Entertainment</option>
                                    <option value="shopping">Shopping</option>
                                    <option value="bills">Bills & Utilities</option>
                                    <option value="health">Healthcare</option>
                                    <option value="education">Education</option>
                                    <option value="salary">Salary</option>
                                    <option value="freelance">Freelance</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="date">Date</label>
                                <input type="date" id="date" required>
                            </div>
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="recurring">
                                    Recurring Transaction
                                </label>
                            </div>
                            <div class="form-group recurring-options" id="recurringOptions" style="display: none;">
                                <label for="recurringType">Repeat Every</label>
                                <select id="recurringType">
                                    <option value="weekly">Week</option>
                                    <option value="monthly">Month</option>
                                    <option value="yearly">Year</option>
                                </select>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Add Transaction
                            </button>
                        </form>
                    </div>

                    <div class="recurring-transactions">
                        <h2>Recurring Transactions</h2>
                        <div id="recurringList" class="recurring-list">
                            <!-- Recurring transactions will be displayed here -->
                        </div>
                    </div>
                </div>

                <div class="right-panel">
                    <div class="charts">
                        <div class="chart-container">
                            <h2>Expense Categories</h2>
                            <canvas id="expenseChart"></canvas>
                        </div>
                        <div class="chart-container">
                            <h2>Income vs Expenses</h2>
                            <canvas id="comparisonChart"></canvas>
                        </div>
                    </div>

                    <div class="recent-transactions">
                        <h2>Recent Transactions</h2>
                        <div class="filters">
                            <select id="filterType">
                                <option value="all">All</option>
                                <option value="income">Income</option>
                                <option value="expense">Expenses</option>
                            </select>
                            <select id="filterCategory">
                                <option value="all">All Categories</option>
                            </select>
                        </div>
                        <div id="transactionsList" class="transactions-list">
                            <!-- Transactions will be displayed here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html> 