import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'auth_provider.dart';
import 'expense_provider.dart';
import 'invoice_provider.dart';

class SyncProvider extends ChangeNotifier {
  static const String _baseUrl = 'http://localhost:8080/api/v1';
  
  bool _isSyncing = false;
  String? _error;
  String? _lastSyncTime;
  int _syncedCount = 0;
  List<String> _conflicts = [];
  List<String> _errors = [];

  bool get isSyncing => _isSyncing;
  String? get error => _error;
  String? get lastSyncTime => _lastSyncTime;
  int get syncedCount => _syncedCount;
  List<String> get conflicts => _conflicts;
  List<String> get errors => _errors;

  void _setSyncing(bool syncing) {
    _isSyncing = syncing;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  void _setLastSyncTime(String time) {
    _lastSyncTime = time;
    notifyListeners();
  }

  void _setSyncResults(int count, List<String> conflicts, List<String> errors) {
    _syncedCount = count;
    _conflicts = conflicts;
    _errors = errors;
    notifyListeners();
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }

  void clearResults() {
    _syncedCount = 0;
    _conflicts = [];
    _errors = [];
    notifyListeners();
  }

  Future<bool> syncAllData({
    required AuthProvider authProvider,
    required ExpenseProvider expenseProvider,
    required InvoiceProvider invoiceProvider,
    BuildContext? context,
  }) async {
    if (_isSyncing) return false;

    _setSyncing(true);
    _setError(null);
    clearResults();

    try {
      final token = authProvider.token;
      if (token == null) {
        throw Exception('No authentication token available');
      }

      final headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      };

      int totalSynced = 0;
      List<String> allConflicts = [];
      List<String> allErrors = [];

      // Sync transactions
      try {
        final response = await http.post(
          Uri.parse('$_baseUrl/sync/transactions'),
          headers: headers,
          body: json.encode({
            'transactions': expenseProvider.transactions.map((t) => t.toJson()).toList(),
          }),
        );

        if (response.statusCode == 200) {
          final data = json.decode(response.body);
          totalSynced += (data['synced_count'] ?? 0) as int;
          if (data['conflicts'] != null) {
            allConflicts.addAll(List<String>.from(data['conflicts']));
          }
          if (data['errors'] != null) {
            allErrors.addAll(List<String>.from(data['errors']));
          }
        } else {
          allErrors.add('Failed to sync transactions: ${response.statusCode}');
        }
      } catch (e) {
        allErrors.add('Error syncing transactions: $e');
      }

      // Sync recurring transactions
      try {
        final response = await http.post(
          Uri.parse('$_baseUrl/sync/recurring'),
          headers: headers,
          body: json.encode({
            'recurring_transactions': expenseProvider.recurringTransactions.map((r) => r.toJson()).toList(),
          }),
        );

        if (response.statusCode == 200) {
          final data = json.decode(response.body);
          totalSynced += (data['synced_count'] ?? 0) as int;
          if (data['conflicts'] != null) {
            allConflicts.addAll(List<String>.from(data['conflicts']));
          }
          if (data['errors'] != null) {
            allErrors.addAll(List<String>.from(data['errors']));
          }
        } else {
          allErrors.add('Failed to sync recurring transactions: ${response.statusCode}');
        }
      } catch (e) {
        allErrors.add('Error syncing recurring transactions: $e');
      }

      // Sync invoices
      try {
        final response = await http.post(
          Uri.parse('$_baseUrl/sync/invoices'),
          headers: headers,
          body: json.encode({
            'invoices': invoiceProvider.invoices.map((i) => i.toJson()).toList(),
          }),
        );

        if (response.statusCode == 200) {
          final data = json.decode(response.body);
          totalSynced += (data['synced_count'] ?? 0) as int;
          if (data['conflicts'] != null) {
            allConflicts.addAll(List<String>.from(data['conflicts']));
          }
          if (data['errors'] != null) {
            allErrors.addAll(List<String>.from(data['errors']));
          }
        } else {
          allErrors.add('Failed to sync invoices: ${response.statusCode}');
        }
      } catch (e) {
        allErrors.add('Error syncing invoices: $e');
      }

      // Sync invoice templates
      try {
        final response = await http.post(
          Uri.parse('$_baseUrl/sync/templates'),
          headers: headers,
          body: json.encode({
            'templates': invoiceProvider.templates.map((t) => t.toJson()).toList(),
          }),
        );

        if (response.statusCode == 200) {
          final data = json.decode(response.body);
          totalSynced += (data['synced_count'] ?? 0) as int;
          if (data['conflicts'] != null) {
            allConflicts.addAll(List<String>.from(data['conflicts']));
          }
          if (data['errors'] != null) {
            allErrors.addAll(List<String>.from(data['errors']));
          }
        } else {
          allErrors.add('Failed to sync invoice templates: ${response.statusCode}');
        }
      } catch (e) {
        allErrors.add('Error syncing invoice templates: $e');
      }

      _setSyncResults(totalSynced, allConflicts, allErrors);
      _setLastSyncTime(DateTime.now().toIso8601String());

      // Show results to user
      if (context != null && context.mounted) {
        _showSyncResults(context, totalSynced, allConflicts, allErrors);
      }

      return allErrors.isEmpty;
    } catch (e) {
      _setError('Sync failed: $e');
      return false;
    } finally {
      _setSyncing(false);
    }
  }

  void _showSyncResults(BuildContext context, int syncedCount, List<String> conflicts, List<String> errors) {
    String message = 'Sync completed successfully!\n\n';
    message += 'Synced: $syncedCount items\n';
    
    if (conflicts.isNotEmpty) {
      message += 'Conflicts: ${conflicts.length}\n';
    }
    
    if (errors.isNotEmpty) {
      message += 'Errors: ${errors.length}';
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sync Results'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(message),
              if (conflicts.isNotEmpty) ...[
                const SizedBox(height: 16),
                const Text('Conflicts:', style: TextStyle(fontWeight: FontWeight.bold)),
                ...conflicts.take(3).map((c) => Text('• $c')),
                if (conflicts.length > 3) Text('... and ${conflicts.length - 3} more'),
              ],
              if (errors.isNotEmpty) ...[
                const SizedBox(height: 16),
                const Text('Errors:', style: TextStyle(fontWeight: FontWeight.bold, color: Colors.red)),
                ...errors.take(3).map((e) => Text('• $e', style: const TextStyle(color: Colors.red))),
                if (errors.length > 3) Text('... and ${errors.length - 3} more', style: const TextStyle(color: Colors.red)),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Future<bool> syncTransactions({
    required AuthProvider authProvider,
    required ExpenseProvider expenseProvider,
  }) async {
    if (_isSyncing) return false;

    _setSyncing(true);
    _setError(null);

    try {
      final token = authProvider.token;
      if (token == null) {
        throw Exception('No authentication token available');
      }

      final response = await http.post(
        Uri.parse('$_baseUrl/sync/transactions'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode({
          'transactions': expenseProvider.transactions.map((t) => t.toJson()).toList(),
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        _setSyncResults(
          data['synced_count'] ?? 0,
          List<String>.from(data['conflicts'] ?? []),
          List<String>.from(data['errors'] ?? []),
        );
        _setLastSyncTime(DateTime.now().toIso8601String());
        return true;
      } else {
        throw Exception('Failed to sync transactions: ${response.statusCode}');
      }
    } catch (e) {
      _setError('Sync failed: $e');
      return false;
    } finally {
      _setSyncing(false);
    }
  }

  Future<bool> syncInvoices({
    required AuthProvider authProvider,
    required InvoiceProvider invoiceProvider,
  }) async {
    if (_isSyncing) return false;

    _setSyncing(true);
    _setError(null);

    try {
      final token = authProvider.token;
      if (token == null) {
        throw Exception('No authentication token available');
      }

      final response = await http.post(
        Uri.parse('$_baseUrl/sync/invoices'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode({
          'invoices': invoiceProvider.invoices.map((i) => i.toJson()).toList(),
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        _setSyncResults(
          data['synced_count'] ?? 0,
          List<String>.from(data['conflicts'] ?? []),
          List<String>.from(data['errors'] ?? []),
        );
        _setLastSyncTime(DateTime.now().toIso8601String());
        return true;
      } else {
        throw Exception('Failed to sync invoices: ${response.statusCode}');
      }
    } catch (e) {
      _setError('Sync failed: $e');
      return false;
    } finally {
      _setSyncing(false);
    }
  }
} 