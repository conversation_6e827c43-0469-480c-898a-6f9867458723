package handlers

import (
	"database/sql"
	"net/http"

	"github.com/gin-gonic/gin"
)

func UpdateRecurringTransactionHandler(c *gin.Context) {
	userID := c.GetInt("userID")
	id := c.Param("id")
	var req struct {
		Description string  `json:"description"`
		Amount      float64 `json:"amount"`
		Category    string  `json:"category"`
		Type        string  `json:"type"`
		Frequency   string  `json:"frequency"`
		StartDate   string  `json:"start_date"`
		EndDate     string  `json:"end_date"`
		Notes       string  `json:"notes"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request"})
		return
	}
	db := c.MustGet("db").(*sql.DB)
	res, err := db.Exec("UPDATE recurring_transactions SET description = $1, amount = $2, category = $3, type = $4, frequency = $5, start_date = $6, end_date = $7, notes = $8 WHERE id = $9 AND user_id = $10", req.Description, req.Amount, req.Category, req.Type, req.Frequency, req.StartDate, req.EndDate, req.Notes, id, userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update recurring transaction"})
		return
	}
	n, _ := res.RowsAffected()
	if n == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Recurring transaction not found"})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Recurring transaction updated"})
}
