import 'dart:convert';
import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../utils/theme.dart';
import '../providers/theme_provider.dart';
import '../providers/expense_provider.dart';
import '../providers/invoice_provider.dart';
import '../providers/notification_provider.dart';
import '../providers/sync_provider.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final expenseProvider = Provider.of<ExpenseProvider>(context);
    return Scaffold(
      // Remove the appBar property
      // appBar: AppBar(
      //   title: const Text('Profile'),
      //   actions: [
      //     Builder(
      //       builder: (context) => IconButton(
      //         icon: Icon(Icons.settings, color: Theme.of(context).iconTheme.color),
      //         onPressed: () => _showSettingsDialog(context),
      //       ),
      //     ),
      //   ],
      // ),
      body: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildProfileHeader(authProvider),
                const SizedBox(height: 24),
                _buildAccountSummary(expenseProvider),
                const SizedBox(height: 32),
                _buildMenuItems(context, authProvider),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildProfileHeader(AuthProvider authProvider) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            const CircleAvatar(
              radius: 50,
              backgroundColor: AppTheme.primaryColor,
              child: Icon(
                Icons.person,
                size: 50,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              authProvider.userEmail ?? 'User',
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'SecureBooks User',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountSummary(ExpenseProvider provider) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 4,
      crossAxisSpacing: 8,
      mainAxisSpacing: 8,
      childAspectRatio: 0.85,
      children: [
        _buildSummaryCard(
          'Total Balance',
          '\$${provider.totalBalance.toStringAsFixed(2)}',
          provider.totalBalance >= 0 ? Colors.green : Colors.red,
          Icons.account_balance_wallet,
        ),
        _buildSummaryCard(
          'Total Income',
          '\$${provider.totalIncome.toStringAsFixed(2)}',
          Colors.green,
          Icons.trending_up,
        ),
        _buildSummaryCard(
          'Total Expenses',
          '\$${provider.totalExpenses.toStringAsFixed(2)}',
          Colors.red,
          Icons.trending_down,
        ),
        _buildSummaryCard(
          'Transactions',
          provider.transactions.length.toString(),
          AppTheme.primaryColor,
          Icons.receipt,
        ),
      ],
    );
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return SizedBox(
      height: 36,
      child: Card(
        elevation: 4,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 4),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Icon(icon, color: color, size: 28),
              const SizedBox(height: 2),
              Text(
                value,
                style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 20),
                textAlign: TextAlign.center,
              ),
              Text(
                title,
                style: const TextStyle(color: Colors.grey, fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMenuItems(BuildContext context, AuthProvider authProvider) {
    return Column(
      children: [
        _buildMenuItem(
          icon: Icons.account_balance_wallet,
          title: 'Account Summary',
          subtitle: 'View your financial overview',
          onTap: () => _showAccountSummary(context),
        ),
        Consumer<SyncProvider>(
          builder: (context, syncProvider, child) {
            return Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                leading: CircleAvatar(
                  backgroundColor: AppTheme.primaryColor,
                  child: syncProvider.isSyncing 
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Icon(Icons.sync, color: Colors.white),
                ),
                title: const Text('Sync Data'),
                subtitle: Text(
                  syncProvider.isSyncing 
                    ? 'Syncing...'
                    : syncProvider.lastSyncTime != null
                      ? 'Last sync: ${DateTime.parse(syncProvider.lastSyncTime!).toLocal().toString().substring(0, 16)}'
                      : 'Sync with cloud storage'
                ),
                trailing: const Icon(Icons.chevron_right),
                onTap: syncProvider.isSyncing ? null : () => _syncData(context),
              ),
            );
          },
        ),
        _buildMenuItem(
          icon: Icons.backup,
          title: 'Backup & Restore',
          subtitle: 'Backup your data',
          onTap: () => showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('Backup & Restore'),
              content: const Text('Would you like to backup or restore your data?'),
              actions: [
                TextButton(
                  onPressed: () => _backupData(context),
                  child: const Text('Backup'),
                ),
                TextButton(
                  onPressed: () => _restoreData(context),
                  child: const Text('Restore'),
                ),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
              ],
            ),
          ),
        ),
        _buildMenuItem(
          icon: Icons.notifications,
          title: 'Notifications',
          subtitle: 'Manage notification settings',
          onTap: () => _showNotificationSettings(context),
        ),
        _buildMenuItem(
          icon: Icons.security,
          title: 'Security',
          subtitle: 'Password and security settings',
          onTap: () => _showSecuritySettings(context),
        ),
        _buildMenuItem(
          icon: Icons.help,
          title: 'Help & Support',
          subtitle: 'Get help and contact support',
          onTap: () => _showHelpSupport(context),
        ),
        _buildMenuItem(
          icon: Icons.info,
          title: 'About',
          subtitle: 'App version and information',
          onTap: () => _showAboutDialog(context),
        ),
        const SizedBox(height: 16),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: () => _signOut(context, authProvider),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Sign Out'),
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          width: double.infinity,
          child: OutlinedButton(
            onPressed: () => _showDeleteAccountDialog(context, authProvider),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.red,
              side: const BorderSide(color: Colors.red),
            ),
            child: const Text('Delete Account'),
          ),
        ),
      ],
    );
  }

  Widget _buildMenuItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: AppTheme.primaryColor,
          child: Icon(icon, color: Colors.white),
        ),
        title: Text(title),
        subtitle: Text(subtitle),
        trailing: const Icon(Icons.chevron_right),
        onTap: onTap,
      ),
    );
  }

  void _showSettingsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const ThemeSettingsDialog(),
    );
  }

  void _showAccountSummary(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Account Summary'),
        content: const Text('Account summary - to be implemented'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _syncData(BuildContext context) async {
    final syncProvider = Provider.of<SyncProvider>(context, listen: false);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final expenseProvider = Provider.of<ExpenseProvider>(context, listen: false);
    final invoiceProvider = Provider.of<InvoiceProvider>(context, listen: false);

    final success = await syncProvider.syncAllData(
      authProvider: authProvider,
      expenseProvider: expenseProvider,
      invoiceProvider: invoiceProvider,
      context: context,
    );

    if (!success && context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(syncProvider.error ?? 'Sync failed'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _backupData(BuildContext context) async {
    final expenseProvider = Provider.of<ExpenseProvider>(context, listen: false);
    final invoiceProvider = Provider.of<InvoiceProvider>(context, listen: false);
    final data = {
      'transactions': expenseProvider.transactions.map((t) => t.toJson()).toList(),
      'invoices': invoiceProvider.invoices.map((i) => i.toJson()).toList(),
    };
    final jsonString = jsonEncode(data);
    final directory = await getApplicationDocumentsDirectory();
    final file = File('${directory.path}/securebooks_backup_${DateTime.now().millisecondsSinceEpoch}.json');
    await file.writeAsString(jsonString);
    if (context.mounted) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Backup Complete'),
          content: Text('Backup saved to:\n${file.path}'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Close'),
            ),
          ],
        ),
      );
    }
  }

  void _restoreData(BuildContext context) async {
    final expenseProvider = Provider.of<ExpenseProvider>(context, listen: false);
    final invoiceProvider = Provider.of<InvoiceProvider>(context, listen: false);
    final result = await FilePicker.platform.pickFiles(type: FileType.custom, allowedExtensions: ['json']);
    if (result != null && result.files.single.path != null) {
      final file = File(result.files.single.path!);
      final jsonString = await file.readAsString();
      final data = jsonDecode(jsonString);
      if (data is Map<String, dynamic>) {
        if (data['transactions'] is List) {
          // Optionally clear existing data or merge
          // expenseProvider.clearTransactions();
          // invoiceProvider.clearInvoices();
          // For now, just print count
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Loaded ${data['transactions'].length} transactions and ${data['invoices']?.length ?? 0} invoices from backup.')),
          );
        }
      }
    }
  }

  void _showNotificationSettings(BuildContext context) async {
    final notificationProvider = Provider.of<NotificationProvider>(context, listen: false);
    bool? result = await showDialog<bool>(
      context: context,
      builder: (context) {
        bool tempValue = notificationProvider.enabled;
        return StatefulBuilder(
          builder: (context, setState) => AlertDialog(
            title: const Text('Notification Settings'),
            content: Row(
              children: [
                const Text('Enable Notifications'),
                const Spacer(),
                Switch(
                  value: tempValue,
                  onChanged: (value) {
                    setState(() => tempValue = value);
                  },
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(tempValue),
                child: const Text('Save'),
              ),
            ],
          ),
        );
      },
    );
    if (result != null) {
      await notificationProvider.setEnabled(result);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(result ? 'Notifications enabled' : 'Notifications disabled'),
        ),
      );
    }
  }

  void _showSecuritySettings(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Security Settings'),
        content: const Text('Security settings - to be implemented'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showHelpSupport(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Help & Support'),
        content: const Text('Help and support - to be implemented'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showAboutDialog(BuildContext context) {
    showAboutDialog(
      context: context,
      applicationName: 'SecureBooks',
      applicationVersion: '1.0.0',
      applicationIcon: const Icon(Icons.account_balance_wallet),
      children: const [
        Text('A comprehensive financial management app with expense tracking and invoicing capabilities.'),
      ],
    );
  }

  void _signOut(BuildContext context, AuthProvider authProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sign Out'),
        content: const Text('Are you sure you want to sign out?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              authProvider.signOut();
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Sign Out'),
          ),
        ],
      ),
    );
  }

  void _showDeleteAccountDialog(BuildContext context, AuthProvider authProvider) {
    showDialog(
      context: context,
      builder: (context) => _DeleteAccountDialog(authProvider: authProvider),
    );
  }

  List<Widget> buildAppBarActions(BuildContext context) {
    return [
      IconButton(
        icon: Icon(Icons.settings, color: Theme.of(context).iconTheme.color),
        onPressed: () => _showSettingsDialog(context),
      ),
    ];
  }
}

class ThemeSettingsDialog extends StatelessWidget {
  const ThemeSettingsDialog({super.key});

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    return AlertDialog(
      title: const Text('Settings'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('Theme'),
          ListTile(
            title: const Text('System'),
            leading: Radio<ThemeMode>(
              value: ThemeMode.system,
              groupValue: themeProvider.themeMode,
              onChanged: (mode) => themeProvider.setTheme(mode!),
            ),
          ),
          ListTile(
            title: const Text('Light'),
            leading: Radio<ThemeMode>(
              value: ThemeMode.light,
              groupValue: themeProvider.themeMode,
              onChanged: (mode) => themeProvider.setTheme(mode!),
            ),
          ),
          ListTile(
            title: const Text('Dark'),
            leading: Radio<ThemeMode>(
              value: ThemeMode.dark,
              groupValue: themeProvider.themeMode,
              onChanged: (mode) => themeProvider.setTheme(mode!),
            ),
          ),
          const SizedBox(height: 16),
          const Divider(),
          const Text('Other settings - to be implemented'),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
      ],
    );
  }
}

class _DeleteAccountDialog extends StatefulWidget {
  final AuthProvider authProvider;
  const _DeleteAccountDialog({required this.authProvider});

  @override
  State<_DeleteAccountDialog> createState() => _DeleteAccountDialogState();
}

class _DeleteAccountDialogState extends State<_DeleteAccountDialog> {
  bool _confirmed = false;
  String _password = '';
  String? _error;
  bool _loading = false;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Delete Account'),
      content: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 340),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'This will permanently delete your account and all associated data. This action cannot be undone.',
              style: TextStyle(color: Colors.red, fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Checkbox(
                  value: _confirmed,
                  onChanged: (v) => setState(() => _confirmed = v ?? false),
                ),
                const Expanded(child: Text('I understand the consequences.')),
              ],
            ),
            const SizedBox(height: 8),
            TextField(
              obscureText: true,
              decoration: const InputDecoration(
                labelText: 'Enter your password',
                border: OutlineInputBorder(),
              ),
              onChanged: (v) => setState(() => _password = v),
            ),
            if (_error != null) ...[
              const SizedBox(height: 8),
              Text(_error!, style: const TextStyle(color: Colors.red)),
            ],
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _loading ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
          onPressed: (!_confirmed || _password.isEmpty || _loading)
              ? null
              : () async {
                  setState(() {
                    _loading = true;
                    _error = null;
                  });
                  final error = await widget.authProvider.deleteAccount(_password);
                  if (error == null) {
                    if (mounted) Navigator.of(context).pop();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Account deleted.'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  } else {
                    setState(() {
                      _error = error.replaceAll('Exception:', '').trim();
                      _loading = false;
                    });
                  }
                },
          child: _loading
              ? const SizedBox(width: 20, height: 20, child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white))
              : const Text('Delete Account'),
        ),
      ],
    );
  }
}