package handlers

import (
	"database/sql"
	"fmt"
	"net/http"
	"time"

	"securebooks/models"

	"github.com/gin-gonic/gin"
)

type SyncInvoiceRequest struct {
	Invoices []models.Invoice `json:"invoices"`
}

type SyncInvoiceResponse struct {
	SyncedCount int      `json:"synced_count"`
	Conflicts   []string `json:"conflicts,omitempty"`
	Errors      []string `json:"errors,omitempty"`
}

func SyncInvoicesHandler(c *gin.Context) {
	var req SyncInvoiceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	db := c.MustGet("db").(*sql.DB)
	userID := c.MustGet("userID").(int)

	response := SyncInvoiceResponse{
		SyncedCount: 0,
		Conflicts:   []string{},
		Errors:      []string{},
	}

	for _, invoice := range req.Invoices {
		// Check if invoice already exists
		var existingID int
		err := db.QueryRow(
			"SELECT id FROM invoices WHERE id = ? AND user_id = ?",
			invoice.ID, userID,
		).Scan(&existingID)

		if err == sql.ErrNoRows {
			// New invoice - insert it
			tx, err := db.Begin()
			if err != nil {
				response.Errors = append(response.Errors, "Failed to begin transaction for invoice "+fmt.Sprintf("%d", invoice.ID)+": "+err.Error())
				continue
			}

			// Insert invoice
			_, err = tx.Exec(`
				INSERT INTO invoices (id, user_id, invoice_number, date, due_date, status, tax_rate, notes, created_at, updated_at)
				VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
			`, invoice.ID, userID, invoice.InvoiceNumber, invoice.Date, invoice.DueDate, invoice.Status, invoice.TaxRate, invoice.Notes, time.Now(), time.Now())

			if err != nil {
				tx.Rollback()
				response.Errors = append(response.Errors, "Failed to insert invoice "+fmt.Sprintf("%d", invoice.ID)+": "+err.Error())
				continue
			}

			// Insert client
			if invoice.Client.ID > 0 {
				_, err = tx.Exec(`
					INSERT INTO invoice_clients (id, invoice_id, name, email, address, phone, created_at, updated_at)
					VALUES (?, ?, ?, ?, ?, ?, ?, ?)
				`, invoice.Client.ID, invoice.ID, invoice.Client.Name, invoice.Client.Email, invoice.Client.Address, invoice.Client.Phone, time.Now(), time.Now())

				if err != nil {
					tx.Rollback()
					response.Errors = append(response.Errors, "Failed to insert client for invoice "+fmt.Sprintf("%d", invoice.ID)+": "+err.Error())
					continue
				}
			}

			// Insert items
			for _, item := range invoice.Items {
				_, err = tx.Exec(`
					INSERT INTO invoice_items (id, invoice_id, description, quantity, price, created_at, updated_at)
					VALUES (?, ?, ?, ?, ?, ?, ?)
				`, item.ID, invoice.ID, item.Description, item.Quantity, item.Price, time.Now(), time.Now())

				if err != nil {
					tx.Rollback()
					response.Errors = append(response.Errors, "Failed to insert item for invoice "+fmt.Sprintf("%d", invoice.ID)+": "+err.Error())
					continue
				}
			}

			if err := tx.Commit(); err != nil {
				response.Errors = append(response.Errors, "Failed to commit transaction for invoice "+fmt.Sprintf("%d", invoice.ID)+": "+err.Error())
			} else {
				response.SyncedCount++
			}
		} else if err != nil {
			response.Errors = append(response.Errors, "Database error for invoice "+fmt.Sprintf("%d", invoice.ID)+": "+err.Error())
		} else {
			// Invoice exists - check if it needs updating
			var lastModified time.Time
			err = db.QueryRow(
				"SELECT updated_at FROM invoices WHERE id = ? AND user_id = ?",
				invoice.ID, userID,
			).Scan(&lastModified)

			if err != nil {
				response.Errors = append(response.Errors, "Failed to get last modified for invoice "+fmt.Sprintf("%d", invoice.ID)+": "+err.Error())
				continue
			}

			// If the incoming invoice is newer, update it
			if invoice.UpdatedAt.After(lastModified) {
				_, err = db.Exec(`
					UPDATE invoices 
					SET invoice_number = ?, date = ?, due_date = ?, status = ?, tax_rate = ?, notes = ?, updated_at = ?
					WHERE id = ? AND user_id = ?
				`, invoice.InvoiceNumber, invoice.Date, invoice.DueDate, invoice.Status, invoice.TaxRate, invoice.Notes, time.Now(), invoice.ID, userID)

				if err != nil {
					response.Errors = append(response.Errors, "Failed to update invoice "+fmt.Sprintf("%d", invoice.ID)+": "+err.Error())
				} else {
					response.SyncedCount++
				}
			} else {
				response.Conflicts = append(response.Conflicts, "Invoice "+fmt.Sprintf("%d", invoice.ID)+" has conflicts (local version is newer)")
			}
		}
	}

	c.JSON(http.StatusOK, response)
}
