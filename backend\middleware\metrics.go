package middleware

import (
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

var (
	// HTTP request metrics
	httpRequestsTotal = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "securebooks_http_requests_total",
			Help: "Total number of HTTP requests",
		},
		[]string{"method", "endpoint", "status_code"},
	)

	httpRequestDuration = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "securebooks_http_request_duration_seconds",
			Help:    "HTTP request duration in seconds",
			Buckets: prometheus.DefBuckets,
		},
		[]string{"method", "endpoint"},
	)

	// Database metrics
	dbConnectionsActive = promauto.NewGauge(
		prometheus.GaugeOpts{
			Name: "securebooks_db_connections_active",
			Help: "Number of active database connections",
		},
	)

	dbQueriesTotal = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "securebooks_db_queries_total",
			Help: "Total number of database queries",
		},
		[]string{"operation", "table"},
	)

	dbQueryDuration = promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "securebooks_db_query_duration_seconds",
			Help:    "Database query duration in seconds",
			Buckets: prometheus.DefBuckets,
		},
		[]string{"operation", "table"},
	)

	// Application metrics
	activeUsers = promauto.NewGauge(
		prometheus.GaugeOpts{
			Name: "securebooks_active_users",
			Help: "Number of currently active users",
		},
	)

	transactionsCreated = promauto.NewCounter(
		prometheus.CounterOpts{
			Name: "securebooks_transactions_created_total",
			Help: "Total number of transactions created",
		},
	)

	invoicesGenerated = promauto.NewCounter(
		prometheus.CounterOpts{
			Name: "securebooks_invoices_generated_total",
			Help: "Total number of invoices generated",
		},
	)

	// Error metrics
	errorsTotal = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "securebooks_errors_total",
			Help: "Total number of errors",
		},
		[]string{"type", "endpoint"},
	)
)

// PrometheusMiddleware creates a Gin middleware for Prometheus metrics
func PrometheusMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.FullPath()
		method := c.Request.Method

		// Process request
		c.Next()

		// Record metrics
		duration := time.Since(start).Seconds()
		statusCode := strconv.Itoa(c.Writer.Status())

		httpRequestsTotal.WithLabelValues(method, path, statusCode).Inc()
		httpRequestDuration.WithLabelValues(method, path).Observe(duration)

		// Record errors if status code indicates an error
		if c.Writer.Status() >= 400 {
			errorsTotal.WithLabelValues("http_error", path).Inc()
		}
	}
}

// RecordDBQuery records database query metrics
func RecordDBQuery(operation, table string, duration time.Duration) {
	dbQueriesTotal.WithLabelValues(operation, table).Inc()
	dbQueryDuration.WithLabelValues(operation, table).Observe(duration.Seconds())
}

// SetActiveDBConnections sets the number of active database connections
func SetActiveDBConnections(count int) {
	dbConnectionsActive.Set(float64(count))
}

// SetActiveUsers sets the number of active users
func SetActiveUsers(count int) {
	activeUsers.Set(float64(count))
}

// IncrementTransactionsCreated increments the transactions created counter
func IncrementTransactionsCreated() {
	transactionsCreated.Inc()
}

// IncrementInvoicesGenerated increments the invoices generated counter
func IncrementInvoicesGenerated() {
	invoicesGenerated.Inc()
}

// RecordError records an error metric
func RecordError(errorType, endpoint string) {
	errorsTotal.WithLabelValues(errorType, endpoint).Inc()
}

// GetMetricsRegistry returns the default Prometheus registry
func GetMetricsRegistry() *prometheus.Registry {
	return prometheus.DefaultRegisterer.(*prometheus.Registry)
}
