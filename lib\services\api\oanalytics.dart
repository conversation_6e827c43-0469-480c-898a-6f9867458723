import 'dart:convert';
import 'package:http/http.dart' as http;

class OAnalyticsApiService {
  final String baseUrl;
  final String? authToken;
  OAnalyticsApiService({required this.baseUrl, required this.authToken});

  Map<String, String> get _headers {
    final headers = {
      'Content-Type': 'application/json',
    };
    if (authToken != null) {
      headers['Authorization'] = 'Bearer $authToken';
    }
    return headers;
  }

  Future<Map<String, dynamic>> getAnalytics() async {
    final response = await http.get(
      Uri.parse('$baseUrl/analytics/summary'),
      headers: _headers,
    );
    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('Failed to get analytics: ${response.body}');
    }
  }
} 