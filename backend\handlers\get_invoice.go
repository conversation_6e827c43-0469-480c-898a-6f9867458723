package handlers

import (
	"database/sql"
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
)

func GetInvoiceHandler(c *gin.Context) {
	userID := c.GetInt("userID")
	id := c.Param("id")
	var i struct {
		ID          int
		ClientName  string
		ClientEmail string
		Total       float64
		Status      string
		DueDate     string
		CreatedAt   string
	}
	db := c.MustGet("db").(*sql.DB)
	err := db.QueryRow("SELECT id, client_name, client_email, total, status, due_date, created_at FROM invoices WHERE id = $1 AND user_id = $2", id, userID).Scan(&i.ID, &i.ClientName, &i.ClientEmail, &i.Total, &i.Status, &i.DueDate, &i.CreatedAt)
	if err == sql.ErrNoRows {
		c.JSON(http.StatusNotFound, gin.H{"error": "Invoice not found"})
		return
	} else if err != nil {
		fmt.Printf("GetInvoiceHandler row scan error: %v\n", err)
		c.<PERSON>(http.StatusInternalServerError, gin.H{"error": "Database error"})
		return
	}
	// Fetch items for this invoice
	itemRows, err := db.Query("SELECT id, description, quantity, price FROM invoice_items WHERE invoice_id = $1 ORDER BY id", i.ID)
	if err != nil {
		fmt.Printf("GetInvoiceHandler item query error: %v\n", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error (items)"})
		return
	}
	items := []gin.H{}
	for itemRows.Next() {
		var item struct {
			ID          int
			Description string
			Quantity    int
			Price       float64
		}
		if err := itemRows.Scan(&item.ID, &item.Description, &item.Quantity, &item.Price); err != nil {
			fmt.Printf("GetInvoiceHandler item scan error: %v\n", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error (item scan)"})
			itemRows.Close()
			return
		}
		items = append(items, gin.H{
			"id":          item.ID,
			"description": item.Description,
			"quantity":    item.Quantity,
			"price":       item.Price,
		})
	}
	itemRows.Close()
	c.JSON(http.StatusOK, gin.H{
		"id":           i.ID,
		"client_name":  i.ClientName,
		"client_email": i.ClientEmail,
		"items":        items,
		"total":        i.Total,
		"status":       i.Status,
		"due_date":     i.DueDate,
		"created_at":   i.CreatedAt,
	})
}
