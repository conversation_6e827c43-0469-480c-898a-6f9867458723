package handlers

import (
	"database/sql"
	"net/http"

	"github.com/gin-gonic/gin"
)

func UpdateInvoiceTemplateHandler(c *gin.Context) {
	userID := c.GetInt("userID")
	id := c.<PERSON>m("id")
	var req struct {
		Name           string  `json:"name"`
		DefaultTaxRate float64 `json:"default_tax_rate"`
		DefaultItems   string  `json:"default_items"`
		Notes          string  `json:"notes"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request"})
		return
	}
	db := c.MustGet("db").(*sql.DB)
	res, err := db.Exec("UPDATE invoice_templates SET name = $1, default_tax_rate = $2, default_items = $3, notes = $4 WHERE id = $5 AND user_id = $6", req.Name, req.DefaultTaxRate, req.DefaultItems, req.Notes, id, userID)
	if err != nil {
		c.J<PERSON>(http.StatusInternalServerError, gin.H{"error": "Failed to update template"})
		return
	}
	n, _ := res.RowsAffected()
	if n == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Template not found"})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Template updated"})
}
