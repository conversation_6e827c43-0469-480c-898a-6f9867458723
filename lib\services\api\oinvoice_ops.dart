import 'package:http/http.dart' as http;
import '../../models/invoice.dart';

class OInvoiceOpsApiService {
  final String baseUrl;
  final String? authToken;
  OInvoiceOpsApiService({required this.baseUrl, required this.authToken});

  Map<String, String> get _headers {
    final headers = {
      'Content-Type': 'application/json',
    };
    if (authToken != null) {
      headers['Authorization'] = 'Bearer $authToken';
    }
    return headers;
  }

  Future<void> sendInvoice(Invoice invoice) async {
    final response = await http.post(
      Uri.parse('$baseUrl/invoices/${invoice.id}/send'),
      headers: _headers,
    );
    if (response.statusCode != 200) {
      throw Exception('Failed to send invoice: ${response.body}');
    }
  }
} 