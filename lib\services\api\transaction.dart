import 'dart:convert';
import 'package:http/http.dart' as http;
import '../../models/transaction.dart';

class TransactionApi {
  static const String baseUrl = 'http://localhost:8080/api/v1';
  String? _authToken;
  void Function()? onUnauthorized;

  void setAuthToken(String token) {
    _authToken = token;
  }

  void setOnUnauthorized(void Function() callback) {
    onUnauthorized = callback;
  }

  Map<String, String> get _headers {
    final headers = {
      'Content-Type': 'application/json',
    };
    if (_authToken != null) {
      headers['Authorization'] = 'Bearer $_authToken';
    }
    return headers;
  }

  void _handleUnauthorized() {
    if (onUnauthorized != null) {
      onUnauthorized!();
    }
  }

  Future<List<Transaction>> getTransactions() async {
    final response = await http.get(
      Uri.parse('$baseUrl/transactions/'),
      headers: _headers,
    );
    
    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      
      if (data is Map && data['transactions'] is List) {
        final transactions = (data['transactions'] as List)
            .map((t) => Transaction.fromJson(t))
            .toList();
        return transactions;
      } else if (data is List) {
        final transactions = data.map<Transaction>((t) => Transaction.fromJson(t)).toList();
        return transactions;
      } else {
        throw Exception('Unexpected response format');
      }
    } else if (response.statusCode == 401) {
      _handleUnauthorized();
      throw Exception('Unauthorized');
    } else {
      throw Exception('Failed to load transactions: ${response.body}');
    }
  }

  Future<Transaction> addTransaction(Transaction transaction) async {
    final response = await http.post(
      Uri.parse('$baseUrl/transactions/'),
      headers: _headers,
      body: json.encode(transaction.toJson()),
    );
    
    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return Transaction.fromJson(data);
    } else if (response.statusCode == 401) {
      _handleUnauthorized();
      throw Exception('Unauthorized');
    } else {
      throw Exception('Failed to add transaction: ${response.body}');
    }
  }

  Future<void> syncTransactions(List<Transaction> transactions) async {
    final response = await http.post(
      Uri.parse('$baseUrl/sync/transactions'),
      headers: _headers,
      body: json.encode({
        'transactions': transactions.map((t) => t.toJson()).toList(),
      }),
    );

    if (response.statusCode == 401) {
      _handleUnauthorized();
      throw Exception('Unauthorized');
    }
    if (response.statusCode != 200) {
      throw Exception('Sync failed:  $baseUrl/sync/transactions');
    }
  }

  Future<void> syncRecurringTransactions(List<RecurringTransaction> recurring) async {
    final response = await http.post(
      Uri.parse('$baseUrl/sync/recurring'),
      headers: _headers,
      body: json.encode({
        'recurring_transactions': recurring.map((r) => r.toJson()).toList(),
      }),
    );

    if (response.statusCode == 401) {
      _handleUnauthorized();
      throw Exception('Unauthorized');
    }
    if (response.statusCode != 200) {
      throw Exception('Sync failed:  $baseUrl/sync/recurring');
    }
  }
} 