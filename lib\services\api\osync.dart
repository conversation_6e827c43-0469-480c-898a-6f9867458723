import 'dart:convert';
import 'package:http/http.dart' as http;
import '../../models/transaction.dart';
import '../../models/invoice.dart';

class OSyncApiService {
  final String baseUrl;
  final String? authToken;
  OSyncApiService({required this.baseUrl, required this.authToken});

  Map<String, String> get _headers {
    final headers = {
      'Content-Type': 'application/json',
    };
    if (authToken != null) {
      headers['Authorization'] = 'Bearer $authToken';
    }
    return headers;
  }

  Future<void> syncTransactions(List<Transaction> transactions) async {
    final response = await http.post(
      Uri.parse('$baseUrl/sync/transactions'),
      headers: _headers,
      body: json.encode({
        'transactions': transactions.map((t) => t.toJson()).toList(),
      }),
    );
    if (response.statusCode != 200) {
      throw Exception('Sync failed: ${response.body}');
    }
  }

  Future<void> syncRecurringTransactions(List recurring) async {
    final response = await http.post(
      Uri.parse('$baseUrl/sync/recurring'),
      headers: _headers,
      body: json.encode({
        'recurring_transactions': recurring.map((r) => r.toJson()).toList(),
      }),
    );
    if (response.statusCode != 200) {
      throw Exception('Sync failed: ${response.body}');
    }
  }

  Future<void> syncInvoices(List<Invoice> invoices) async {
    final response = await http.post(
      Uri.parse('$baseUrl/sync/invoices'),
      headers: _headers,
      body: json.encode({
        'invoices': invoices.map((i) => i.toJson()).toList(),
      }),
    );
    if (response.statusCode != 200) {
      throw Exception('Sync failed: ${response.body}');
    }
  }

  Future<void> syncInvoiceTemplates(List templates) async {
    final response = await http.post(
      Uri.parse('$baseUrl/sync/templates'),
      headers: _headers,
      body: json.encode({
        'templates': templates.map((t) => t.toJson()).toList(),
      }),
    );
    if (response.statusCode != 200) {
      throw Exception('Sync failed: ${response.body}');
    }
  }
} 