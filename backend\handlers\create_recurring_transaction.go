package handlers

import (
	"database/sql"
	"net/http"

	"github.com/gin-gonic/gin"
)

func CreateRecurringTransactionHandler(c *gin.Context) {
	userID := c.GetInt("userID")
	var req struct {
		Description string  `json:"description"`
		Amount      float64 `json:"amount"`
		Category    string  `json:"category"`
		Type        string  `json:"type"`
		Frequency   string  `json:"frequency"`
		StartDate   string  `json:"start_date"`
		EndDate     string  `json:"end_date"`
		Notes       string  `json:"notes"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request"})
		return
	}
	var id int
	db := c.MustGet("db").(*sql.DB)
	err := db.QueryRow(
		"INSERT INTO recurring_transactions (user_id, description, amount, category, type, frequency, start_date, end_date, notes) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9) RETURNING id",
		userID, req.Description, req.Amount, req.Category, req.Type, req.Frequency, req.StartDate, req.EndDate, req.Notes,
	).Scan(&id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create recurring transaction"})
		return
	}
	c.JSON(http.StatusOK, gin.H{"id": id})
}
