import 'dart:convert';
import 'package:http/http.dart' as http;
import '../../models/transaction.dart';
import '../../models/invoice.dart';

class SyncApi {
  static const String baseUrl = 'http://localhost:8080/api/v1';
  String? _authToken;
  void Function()? onUnauthorized;

  void setAuthToken(String token) {
    _authToken = token;
  }

  void setOnUnauthorized(void Function() callback) {
    onUnauthorized = callback;
  }

  Map<String, String> get _headers {
    final headers = {
      'Content-Type': 'application/json',
    };
    if (_authToken != null) {
      headers['Authorization'] = 'Bearer $_authToken';
    }
    return headers;
  }

  void _handleUnauthorized() {
    if (onUnauthorized != null) {
      onUnauthorized!();
    }
  }

  Future<void> syncTransactions(List<Transaction> transactions) async {
    final response = await http.post(
      Uri.parse('$baseUrl/sync/transactions'),
      headers: _headers,
      body: json.encode({
        'transactions': transactions.map((t) => t.toJson()).toList(),
      }),
    );

    if (response.statusCode == 401) {
      _handleUnauthorized();
      throw Exception('Unauthorized');
    }
    if (response.statusCode != 200) {
      throw Exception('Sync failed:  ${response.body}');
    }
  }

  Future<void> syncRecurringTransactions(List<RecurringTransaction> recurring) async {
    final response = await http.post(
      Uri.parse('$baseUrl/sync/recurring'),
      headers: _headers,
      body: json.encode({
        'recurring_transactions': recurring.map((r) => r.toJson()).toList(),
      }),
    );

    if (response.statusCode == 401) {
      _handleUnauthorized();
      throw Exception('Unauthorized');
    }
    if (response.statusCode != 200) {
      throw Exception('Sync failed:  ${response.body}');
    }
  }

  Future<void> syncInvoices(List<Invoice> invoices) async {
    final response = await http.post(
      Uri.parse('$baseUrl/sync/invoices'),
      headers: _headers,
      body: json.encode({
        'invoices': invoices.map((i) => i.toJson()).toList(),
      }),
    );

    if (response.statusCode == 401) {
      _handleUnauthorized();
      throw Exception('Unauthorized');
    }
    if (response.statusCode != 200) {
      throw Exception('Sync failed:  ${response.body}');
    }
  }

  Future<void> syncInvoiceTemplates(List<InvoiceTemplate> templates) async {
    final response = await http.post(
      Uri.parse('$baseUrl/sync/templates'),
      headers: _headers,
      body: json.encode({
        'templates': templates.map((t) => t.toJson()).toList(),
      }),
    );

    if (response.statusCode == 401) {
      _handleUnauthorized();
      throw Exception('Unauthorized');
    }
    if (response.statusCode != 200) {
      throw Exception('Sync failed:  ${response.body}');
    }
  }
} 