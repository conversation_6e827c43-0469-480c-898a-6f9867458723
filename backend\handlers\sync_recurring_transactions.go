package handlers

import (
	"database/sql"
	"fmt"
	"net/http"
	"time"

	"securebooks/models"

	"github.com/gin-gonic/gin"
)

type SyncRecurringTransactionRequest struct {
	RecurringTransactions []models.RecurringTransaction `json:"recurring_transactions"`
}

type SyncRecurringTransactionResponse struct {
	SyncedCount int      `json:"synced_count"`
	Conflicts   []string `json:"conflicts,omitempty"`
	Errors      []string `json:"errors,omitempty"`
}

func SyncRecurringTransactionsHandler(c *gin.Context) {
	var req SyncRecurringTransactionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	db := c.MustGet("db").(*sql.DB)
	userID := c.MustGet("userID").(int)

	response := SyncRecurringTransactionResponse{
		SyncedCount: 0,
		Conflicts:   []string{},
		Errors:      []string{},
	}

	for _, recurring := range req.RecurringTransactions {
		// Check if recurring transaction already exists
		var existingID int
		err := db.QueryRow(
			"SELECT id FROM recurring_transactions WHERE id = ? AND user_id = ?",
			recurring.ID, userID,
		).Scan(&existingID)

		if err == sql.ErrNoRows {
			// New recurring transaction - insert it
			_, err = db.Exec(`
				INSERT INTO recurring_transactions (id, user_id, description, amount, category, type, frequency, start_date, end_date, notes, created_at, updated_at)
				VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
			`, recurring.ID, userID, recurring.Description, recurring.Amount, recurring.Category, recurring.Type, recurring.Frequency, recurring.StartDate, recurring.EndDate, recurring.Notes, time.Now(), time.Now())

			if err != nil {
				response.Errors = append(response.Errors, "Failed to insert recurring transaction "+fmt.Sprintf("%d", recurring.ID)+": "+err.Error())
			} else {
				response.SyncedCount++
			}
		} else if err != nil {
			response.Errors = append(response.Errors, "Database error for recurring transaction "+fmt.Sprintf("%d", recurring.ID)+": "+err.Error())
		} else {
			// Recurring transaction exists - check if it needs updating
			var lastModified time.Time
			err = db.QueryRow(
				"SELECT updated_at FROM recurring_transactions WHERE id = ? AND user_id = ?",
				recurring.ID, userID,
			).Scan(&lastModified)

			if err != nil {
				response.Errors = append(response.Errors, "Failed to get last modified for recurring transaction "+fmt.Sprintf("%d", recurring.ID)+": "+err.Error())
				continue
			}

			// If the incoming recurring transaction is newer, update it
			if recurring.UpdatedAt.After(lastModified) {
				_, err = db.Exec(`
					UPDATE recurring_transactions 
					SET description = ?, amount = ?, category = ?, type = ?, frequency = ?, start_date = ?, end_date = ?, notes = ?, updated_at = ?
					WHERE id = ? AND user_id = ?
				`, recurring.Description, recurring.Amount, recurring.Category, recurring.Type, recurring.Frequency, recurring.StartDate, recurring.EndDate, recurring.Notes, time.Now(), recurring.ID, userID)

				if err != nil {
					response.Errors = append(response.Errors, "Failed to update recurring transaction "+fmt.Sprintf("%d", recurring.ID)+": "+err.Error())
				} else {
					response.SyncedCount++
				}
			} else {
				response.Conflicts = append(response.Conflicts, "Recurring transaction "+fmt.Sprintf("%d", recurring.ID)+" has conflicts (local version is newer)")
			}
		}
	}

	c.JSON(http.StatusOK, response)
}
