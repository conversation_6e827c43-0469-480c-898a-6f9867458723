import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import '../models/invoice.dart';
import '../services/database_service.dart';
import '../services/api/invoice.dart';
import '../services/pdf_service.dart';
import 'auth_provider.dart';
import '../services/notification_service.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

class InvoiceProvider with ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService();
  final InvoiceApi apiService;
  final PdfService _pdfService = PdfService();
  InvoiceProvider({required this.apiService});
  
  List<Invoice> _invoices = [];
  List<InvoiceTemplate> _templates = [];
  Invoice? _currentInvoice;
  bool _isLoading = false;
  String? _error;

  List<Invoice> get invoices => _invoices;
  List<InvoiceTemplate> get templates => _templates;
  Invoice? get currentInvoice => _currentInvoice;
  bool get isLoading => _isLoading;
  String? get error => _error;

  double get totalInvoiced {
    return _invoices.fold(0.0, (sum, invoice) => sum + invoice.total);
  }

  double get totalPaid {
    return _invoices
        .where((invoice) => invoice.status == 'paid')
        .fold(0.0, (sum, invoice) => sum + invoice.total);
  }

  double get totalOutstanding {
    return _invoices
        .where((invoice) => invoice.status == 'sent' || invoice.status == 'overdue')
        .fold(0.0, (sum, invoice) => sum + invoice.total);
  }

  static const String _baseUrl = 'http://localhost:8080/api/v1';

  Future<void> loadInvoices({AuthProvider? authProvider}) async {
    _setLoading(true);
    try {
      if (authProvider?.token != null) {
        apiService.setAuthToken(authProvider!.token!);
      }
      final data = await apiService.getInvoices();
      _invoices = data;
      _error = null;
    } catch (e, stack) {
      debugPrint('Failed to load invoices: $e\n$stack');
      _error = 'Failed to load invoices: $e';
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> isOnline() async {
    final connectivityResult = await Connectivity().checkConnectivity();
    return connectivityResult != ConnectivityResult.none;
  }

  Future<Invoice> createInvoice(Invoice invoice, {AuthProvider? authProvider, BuildContext? context}) async {
    _setLoading(true);
    try {
      if (!await isOnline()) {
        _error = 'No internet connection';
        if (context != null) {
          NotificationService.showErrorNotification(
            context,
            'No internet connection',
            title: 'Offline',
          );
        }
        throw Exception('No internet connection');
      }
      if (authProvider?.token != null) {
        apiService.setAuthToken(authProvider!.token!);
      }
      // Create invoice and get full invoice (with invoice number) from backend
      final savedInvoice = await apiService.createInvoice(invoice);
      _invoices.add(savedInvoice);
      _error = null;
      notifyListeners();
      if (context != null) {
        NotificationService.showSuccessNotification(
          context,
          'Invoice 24{savedInvoice.invoiceNumber} successfully created',
          title: 'Invoice Created',
        );
      }
      return savedInvoice;
    } catch (e, stack) {
      debugPrint('Failed to create invoice: $e\n$stack');
      _error = 'Failed to create invoice: $e';
      if (context != null) {
        NotificationService.showErrorNotification(
          context,
          'Failed to create invoice: $e',
          title: 'Error',
        );
      }
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  Future<void> updateInvoice(Invoice invoice, {AuthProvider? authProvider, BuildContext? context}) async {
    _setLoading(true);
    try {
      if (!await isOnline()) {
        _error = 'No internet connection';
        if (context != null) {
          NotificationService.showErrorNotification(
            context,
            'No internet connection',
            title: 'Offline',
          );
        }
        return;
      }
      final token = authProvider?.token;
      final response = await http.put(
        Uri.parse('$_baseUrl/invoices/${invoice.id}'),
        headers: {
          'Content-Type': 'application/json',
          if (token != null) 'Authorization': 'Bearer $token',
        },
        body: jsonEncode(invoice.toJson()),
      );
      final data = jsonDecode(response.body);
      if (response.statusCode == 200) {
        final updatedInvoice = Invoice.fromJson(data);
        final index = _invoices.indexWhere((i) => i.id == invoice.id);
        if (index != -1) {
          _invoices[index] = updatedInvoice;
        }
        _error = null;
        notifyListeners();
        if (context != null) {
          NotificationService.showSuccessNotification(
            context,
            'Invoice ${invoice.invoiceNumber} successfully updated',
            title: 'Invoice Updated',
          );
        }
      } else {
        _error = data['error'] ?? 'Failed to update invoice';
        if (context != null) {
          NotificationService.showErrorNotification(
            context,
            _error!,
            title: 'Update Failed',
          );
        }
      }
    } catch (e) {
      _error = 'Failed to update invoice: $e';
      if (context != null) {
        NotificationService.showErrorNotification(
          context,
          'Failed to update invoice: ${e.toString().replaceAll('Exception: ', '')}',
          title: 'Error',
        );
      }
    } finally {
      _setLoading(false);
    }
  }

  Future<void> deleteInvoice(String id, {AuthProvider? authProvider, BuildContext? context}) async {
    _setLoading(true);
    try {
      if (!await isOnline()) {
        _error = 'No internet connection';
        if (context != null) {
          NotificationService.showErrorNotification(
            context,
            'No internet connection',
            title: 'Offline',
          );
        }
        return;
      }
      final token = authProvider?.token;
      final response = await http.delete(
        Uri.parse('$_baseUrl/invoices/$id'),
        headers: {
          'Content-Type': 'application/json',
          if (token != null) 'Authorization': 'Bearer $token',
        },
      );
      final data = jsonDecode(response.body);
      if (response.statusCode == 200) {
        _invoices.removeWhere((i) => i.id == id);
        _error = null;
        notifyListeners();
        if (context != null) {
          NotificationService.showSuccessNotification(
            context,
            'Invoice successfully deleted',
            title: 'Invoice Deleted',
          );
        }
      } else {
        _error = data['error'] ?? 'Failed to delete invoice';
        if (context != null) {
          NotificationService.showErrorNotification(
            context,
            _error!,
            title: 'Delete Failed',
          );
        }
      }
    } catch (e) {
      _error = 'Failed to delete invoice: $e';
      if (context != null) {
        NotificationService.showErrorNotification(
          context,
          'Failed to delete invoice: ${e.toString().replaceAll('Exception: ', '')}',
          title: 'Error',
        );
      }
    } finally {
      _setLoading(false);
    }
  }

  Future<void> loadInvoiceTemplates({AuthProvider? authProvider}) async {
    _setLoading(true);
    try {
      final token = authProvider?.token;
      if (token == null) {
        _error = 'No authentication token available';
        return;
      }
      
      print('Loading invoice templates from: $_baseUrl/templates/');
      final response = await http.get(
        Uri.parse('$_baseUrl/templates/'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );
      
      print('Response status: ${response.statusCode}');
      print('Response body: ${response.body}');
      
      if (response.statusCode == 401) {
        _error = 'Authentication failed. Please log in again.';
        return;
      }
      
      if (response.statusCode != 200) {
        _error = 'Server error: ${response.statusCode}';
        return;
      }
      
      final data = jsonDecode(response.body);
      print('Decoded data: $data');
      
      if (data is Map<String, dynamic> && data.containsKey('templates')) {
        final templatesList = data['templates'] as List;
        _templates = templatesList.map<InvoiceTemplate>((t) => InvoiceTemplate.fromJson(t)).toList();
        _error = null;
        print('Loaded ${_templates.length} templates');
      } else {
        _error = 'Invalid response format from server';
        print('Invalid response format: $data');
      }
    } catch (e) {
      _error = 'Failed to load invoice templates: $e';
      print('Error loading templates: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> saveTemplate(InvoiceTemplate template, {AuthProvider? authProvider}) async {
    _setLoading(true);
    try {
      final token = authProvider?.token;
      final response = await http.post(
        Uri.parse('$_baseUrl/templates/'),
        headers: {
          'Content-Type': 'application/json',
          if (token != null) 'Authorization': 'Bearer $token',
        },
        body: jsonEncode(template.toJson()),
      );
      final data = jsonDecode(response.body);
      if (response.statusCode == 200) {
        // Backend returns {"id": id}, so we need to reload templates to get the full data
        await loadInvoiceTemplates(authProvider: authProvider);
        _error = null;
        notifyListeners();
      } else {
        _error = data['error'] ?? 'Failed to save template';
      }
    } catch (e) {
      _error = 'Failed to save template: $e';
    } finally {
      _setLoading(false);
    }
  }

  Future<void> updateTemplate({
    required String id,
    required String name,
    required double defaultTaxRate,
    String? notes,
    required AuthProvider authProvider,
  }) async {
    _setLoading(true);
    try {
      final token = authProvider.token;
      final response = await http.put(
        Uri.parse('$_baseUrl/templates/$id'),
        headers: {
          'Content-Type': 'application/json',
          if (token != null) 'Authorization': 'Bearer $token',
        },
        body: jsonEncode({
          'name': name,
          'default_tax_rate': defaultTaxRate,
          'notes': notes,
        }),
      );
      if (response.statusCode == 200) {
        await loadInvoiceTemplates(authProvider: authProvider);
        _error = null;
        notifyListeners();
      } else {
        _error = 'Failed to update template';
      }
    } catch (e) {
      _error = 'Failed to update template: $e';
    } finally {
      _setLoading(false);
    }
  }

  Future<void> deleteTemplate(String id, {AuthProvider? authProvider}) async {
    _setLoading(true);
    try {
      final token = authProvider?.token;
      final response = await http.delete(
        Uri.parse('$_baseUrl/templates/$id'),
        headers: {
          'Content-Type': 'application/json',
          if (token != null) 'Authorization': 'Bearer $token',
        },
      );
      final data = jsonDecode(response.body);
      if (response.statusCode == 200) {
        _templates.removeWhere((t) => t.id == id);
        _error = null;
        notifyListeners();
      } else {
        _error = data['error'] ?? 'Failed to delete template';
      }
    } catch (e) {
      _error = 'Failed to delete template: $e';
    } finally {
      _setLoading(false);
    }
  }

  Future<void> createTemplate({
    required String name,
    required double defaultTaxRate,
    String? notes,
    required AuthProvider authProvider,
  }) async {
    _setLoading(true);
    try {
      final token = authProvider.token;
      final response = await http.post(
        Uri.parse('$_baseUrl/templates/'),
        headers: {
          'Content-Type': 'application/json',
          if (token != null) 'Authorization': 'Bearer $token',
        },
        body: jsonEncode({
          'name': name,
          'default_tax_rate': defaultTaxRate,
          'notes': notes,
        }),
      );
      if (response.statusCode == 200) {
        await loadInvoiceTemplates(authProvider: authProvider);
        _error = null;
        notifyListeners();
      } else {
        _error = 'Failed to create template';
      }
    } catch (e) {
      _error = 'Failed to create template: $e';
    } finally {
      _setLoading(false);
    }
  }


  Future<void> sendInvoice(Invoice invoice, {AuthProvider? authProvider, BuildContext? context}) async {
    _setLoading(true);
    try {
      final token = authProvider?.token;
      if (token == null) {
        throw Exception('No authentication token available');
      }

      final response = await http.post(
        Uri.parse('$_baseUrl/invoices/${invoice.id}/send'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode({
          'invoice_id': int.parse(invoice.id),
        }),
      );

      final data = jsonDecode(response.body);
      if (response.statusCode == 200) {
        // Update invoice status to 'sent'
        final sentInvoice = invoice.copyWith(status: 'sent');
        final index = _invoices.indexWhere((i) => i.id == invoice.id);
        if (index != -1) {
          _invoices[index] = sentInvoice;
        }
        _error = null;
        notifyListeners();
        
        if (context != null && context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Invoice sent successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        _error = data['error'] ?? 'Failed to send invoice';
        if (context != null && context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(_error!),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      _error = 'Failed to send invoice: $e';
      if (context != null && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_error!),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      _setLoading(false);
    }
  }

  Future<Invoice> createDraftInvoice({AuthProvider? authProvider}) async {
    if (authProvider?.token != null) {
      apiService.setAuthToken(authProvider!.token!);
    }
    // Step 1: Create draft invoice in backend
    final draft = await apiService.createDraftInvoice(authProvider?.userId ?? '');
    // Step 2: Generate invoice number
    String userId = (authProvider?.userId ?? '').padLeft(5, '0');
    String invoiceId = draft.id.toString(); // Use backend ID as string, no padLeft
    String invoiceNumber = '$userId-$invoiceId';
    // Step 3: Update invoice in backend with invoice number
    final updatedDraft = draft.copyWith(invoiceNumber: invoiceNumber, id: invoiceId);
    await updateInvoice(updatedDraft, authProvider: authProvider);
    // Step 4: Add to local list and return
    _invoices.add(updatedDraft);
    notifyListeners();
    return updatedDraft;
  }

  Future<void> deleteInvoiceById(String id, {AuthProvider? authProvider}) async {
    if (authProvider?.token != null) {
      apiService.setAuthToken(authProvider!.token!);
    }
    await apiService.deleteInvoice(id);
    _invoices.removeWhere((i) => i.id.toString() == id.toString());
    notifyListeners();
  }

  Future<Invoice> fetchInvoiceById(String id, {AuthProvider? authProvider}) async {
    if (authProvider?.token != null) {
      apiService.setAuthToken(authProvider!.token!);
    }
    final invoice = await apiService.getInvoiceById(id);
    return invoice;
  }

  void setCurrentInvoice(Invoice? invoice) {
    _currentInvoice = invoice;
    notifyListeners();
  }

  Invoice createNewInvoice() {
    final now = DateTime.now();
    final dueDate = DateTime(now.year, now.month, now.day + 30);
    return Invoice(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      invoiceNumber: '', // will be set after backend returns id
      date: now,
      dueDate: dueDate,
      client: Client(name: '', email: ''),
      items: [],
      status: 'draft',
    );
  }

  Invoice createFromTemplate(InvoiceTemplate template) {
    final now = DateTime.now();
    final dueDate = DateTime(now.year, now.month, now.day + 30);
    
    return Invoice(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      invoiceNumber: 'INV-${DateTime.now().millisecondsSinceEpoch}',
      date: now,
      dueDate: dueDate,
      client: Client(name: '', email: ''),
      items: List.from(template.defaultItems),
      taxRate: template.defaultTaxRate,
      status: 'draft',
      notes: template.notes,
    );
  }

  Future<void> _syncToCloud() async {
    try {
      await apiService.syncInvoices(_invoices);
      await apiService.syncInvoiceTemplates(_templates);
    } catch (e) {
      // Handle sync errors gracefully
      debugPrint('Sync failed: $e');
    }
  }

  Future<void> generatePDF(Invoice invoice, {AuthProvider? authProvider, BuildContext? context}) async {
    _setLoading(true);
    try {
      final token = authProvider?.token;
      if (token == null) {
        throw Exception('No authentication token available');
      }

      final response = await http.post(
        Uri.parse('$_baseUrl/pdf/invoice'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode({
          'invoice_id': int.parse(invoice.id),
        }),
      );

      if (response.statusCode == 200) {
        // Save PDF to file
        final directory = await getApplicationDocumentsDirectory();
        final file = File('${directory.path}/invoice_${invoice.invoiceNumber}.pdf');
        await file.writeAsBytes(response.bodyBytes);
        
        if (context != null && context.mounted) {
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('PDF Generated'),
              content: Text('PDF saved to:\n${file.path}'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Close'),
                ),
              ],
            ),
          );
        }
      } else {
        throw Exception('Failed to generate PDF: ${response.statusCode}');
      }
    } catch (e) {
      _error = 'Failed to generate PDF: $e';
      if (context != null && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_error!),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      _setLoading(false);
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }
} 