groups:
  - name: securebooks_alerts
    rules:
      # Application Health Alerts
      - alert: ApplicationDown
        expr: up{job="securebooks-app"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "SecureBooks application is down"
          description: "SecureBooks application has been down for more than 1 minute."

      - alert: HighErrorRate
        expr: rate(securebooks_errors_total[5m]) > 0.1
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value }} errors per second over the last 5 minutes."

      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(securebooks_http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High response time"
          description: "95th percentile response time is {{ $value }} seconds."

      # Database Alerts
      - alert: DatabaseConnectionsHigh
        expr: securebooks_db_connections_active > 80
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High database connection usage"
          description: "Database connections are at {{ $value }}, approaching the limit."

      - alert: DatabaseDown
        expr: up{job="postgres-exporter"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Database is down"
          description: "PostgreSQL database has been down for more than 1 minute."

      # System Resource Alerts
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage"
          description: "CPU usage is {{ $value }}% on {{ $labels.instance }}."

      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage"
          description: "Memory usage is {{ $value }}% on {{ $labels.instance }}."

      - alert: DiskSpaceLow
        expr: (1 - (node_filesystem_avail_bytes / node_filesystem_size_bytes)) * 100 > 90
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Low disk space"
          description: "Disk usage is {{ $value }}% on {{ $labels.instance }} {{ $labels.mountpoint }}."

      # Container Alerts
      - alert: ContainerHighCPU
        expr: rate(container_cpu_usage_seconds_total{name!=""}[5m]) * 100 > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Container high CPU usage"
          description: "Container {{ $labels.name }} CPU usage is {{ $value }}%."

      - alert: ContainerHighMemory
        expr: (container_memory_usage_bytes{name!=""} / container_spec_memory_limit_bytes{name!=""}) * 100 > 90
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Container high memory usage"
          description: "Container {{ $labels.name }} memory usage is {{ $value }}%."

      # Redis Alerts
      - alert: RedisDown
        expr: up{job="redis-exporter"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Redis is down"
          description: "Redis has been down for more than 1 minute."

      - alert: RedisHighMemoryUsage
        expr: redis_memory_used_bytes / redis_memory_max_bytes * 100 > 90
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Redis high memory usage"
          description: "Redis memory usage is {{ $value }}%."

      # Business Logic Alerts
      - alert: HighTransactionVolume
        expr: rate(securebooks_transactions_created_total[5m]) > 100
        for: 2m
        labels:
          severity: info
        annotations:
          summary: "High transaction volume"
          description: "Transaction creation rate is {{ $value }} per second."

      - alert: NoTransactionsCreated
        expr: increase(securebooks_transactions_created_total[1h]) == 0
        for: 1h
        labels:
          severity: warning
        annotations:
          summary: "No transactions created"
          description: "No transactions have been created in the last hour."

      # Security Alerts
      - alert: HighFailedLoginAttempts
        expr: rate(securebooks_http_requests_total{endpoint="/login",status_code=~"4.."}[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High failed login attempts"
          description: "Failed login rate is {{ $value }} per second."

      # Health Check Alerts
      - alert: HealthCheckFailing
        expr: probe_success{job="blackbox"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Health check failing"
          description: "Health check for {{ $labels.instance }} has been failing for more than 1 minute."
