package handlers

import (
	"database/sql"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"golang.org/x/crypto/bcrypt"

	"securebooks/utils"
)

func LoginHandler(c *gin.Context) {
	var req struct {
		Email    string `json:"email"`
		Password string `json:"password"`
	}
	if err := c.ShouldBindJ<PERSON>(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request"})
		return
	}
	fieldErrors := gin.H{}
	if len(req.Password) < 6 {
		fieldErrors["password"] = "Password must be at least 6 characters"
	}
	if len(req.Email) == 0 || !strings.Contains(req.Email, "@") {
		fieldErrors["email"] = "Invalid email address"
	}
	if len(fieldErrors) > 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":        "Login failed",
			"field_errors": fieldErrors,
		})
		return
	}
	// Look up user
	var userID int
	var passwordHash string
	db := c.MustGet("db").(*sql.DB)
	err := db.QueryRow("SELECT id, password_hash FROM users WHERE email=$1", req.Email).Scan(&userID, &passwordHash)
	if err == sql.ErrNoRows {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":        "Login failed",
			"field_errors": gin.H{"email": "No account found with this email"},
		})
		return
	} else if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error"})
		return
	}
	// Compare password
	err = bcrypt.CompareHashAndPassword([]byte(passwordHash), []byte(req.Password))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":        "Login failed",
			"field_errors": gin.H{"password": "Incorrect password"},
		})
		return
	}
	jwtToken, err := GenerateJWT(userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate token"})
		return
	}
	// Generate and store refresh token
	refreshToken, err := utils.GenerateRefreshTokenString()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate refresh token"})
		return
	}
	expiresAt := time.Now().Add(30 * 24 * time.Hour) // 30 days
	_, err = db.Exec(
		"INSERT INTO refresh_tokens (user_id, token, expires_at, created_at, updated_at) VALUES ($1, $2, $3, NOW(), NOW())",
		userID, refreshToken, expiresAt,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to store refresh token"})
		return
	}
	c.JSON(http.StatusOK, gin.H{"token": jwtToken, "refreshToken": refreshToken, "userId": userID})
}
