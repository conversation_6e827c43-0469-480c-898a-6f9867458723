class Budget {
  final int id;
  final int userId;
  final String? category;
  final String periodType; // 'monthly' or 'yearly'
  final DateTime periodStart;
  final double amount;

  Budget({
    required this.id,
    required this.userId,
    this.category,
    required this.periodType,
    required this.periodStart,
    required this.amount,
  });

  factory Budget.fromJson(Map<String, dynamic> json) => Budget(
    id: json['id'],
    userId: json['user_id'],
    category: json['category'],
    periodType: json['period_type'],
    periodStart: DateTime.parse(json['period_start']),
    amount: (json['amount'] as num).toDouble(),
  );

  Map<String, dynamic> toJson() => {
    'id': id,
    'user_id': userId,
    'category': category,
    'period_type': periodType,
    'period_start': periodStart.toIso8601String(),
    'amount': amount,
  };
}

class BudgetStatus {
  final int budgetId;
  final String? category;
  final String periodType;
  final DateTime periodStart;
  final double amount;
  final double actual;
  final double percentUsed;
  final String alertLevel; // 'ok', 'approaching', 'over'

  BudgetStatus({
    required this.budgetId,
    this.category,
    required this.periodType,
    required this.periodStart,
    required this.amount,
    required this.actual,
    required this.percentUsed,
    required this.alertLevel,
  });

  factory BudgetStatus.fromJson(Map<String, dynamic> json) => BudgetStatus(
    budgetId: json['budget_id'],
    category: json['category'],
    periodType: json['period_type'],
    periodStart: DateTime.parse(json['period_start']),
    amount: (json['amount'] as num).toDouble(),
    actual: (json['actual'] as num).toDouble(),
    percentUsed: (json['percent_used'] as num).toDouble(),
    alertLevel: json['alert_level'],
  );
} 