package handlers

import (
	"database/sql"
	"net/http"
	"time"

	"securebooks/utils"

	"github.com/gin-gonic/gin"
)

func RefreshTokenHandler(c *gin.Context) {
	var req struct {
		RefreshToken string `json:"refreshToken"`
	}
	if err := c.ShouldBindJ<PERSON>N(&req); err != nil || req.RefreshToken == "" {
		c.J<PERSON>(http.StatusBadRequest, gin.H{"error": "Refresh token required"})
		return
	}
	db := c.MustGet("db").(*sql.DB)
	var userID int
	var expiresAt time.Time
	// Validate refresh token
	err := db.QueryRow("SELECT user_id, expires_at FROM refresh_tokens WHERE token = $1 AND deleted_at IS NULL", req.RefreshToken).Scan(&userID, &expiresAt)
	if err == sql.ErrNoRows {
		c.<PERSON>(http.StatusUnauthorized, gin.H{"error": "Invalid refresh token"})
		return
	} else if err != nil {
		c.<PERSON>(http.StatusInternalServerError, gin.H{"error": "Database error"})
		return
	}
	if time.Now().After(expiresAt) {
		// Remove expired token
		_, _ = db.Exec("DELETE FROM refresh_tokens WHERE token = $1", req.RefreshToken)
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Refresh token expired"})
		return
	}
	// Remove old refresh token (rotate)
	_, _ = db.Exec("DELETE FROM refresh_tokens WHERE token = $1", req.RefreshToken)
	// Issue new JWT and refresh token
	jwtToken, err := GenerateJWT(userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate token"})
		return
	}
	newRefreshToken, err := utils.GenerateRefreshTokenString()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate refresh token"})
		return
	}
	expiresAt = time.Now().Add(30 * 24 * time.Hour)
	_, err = db.Exec(
		"INSERT INTO refresh_tokens (user_id, token, expires_at, created_at, updated_at) VALUES ($1, $2, $3, NOW(), NOW())",
		userID, newRefreshToken, expiresAt,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to store refresh token"})
		return
	}
	c.JSON(http.StatusOK, gin.H{"token": jwtToken, "refreshToken": newRefreshToken})
}
