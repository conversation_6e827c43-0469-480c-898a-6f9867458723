package handlers

import (
	"database/sql"
	"net/http"

	"github.com/gin-gonic/gin"
)

func CreateInvoiceTemplateHandler(c *gin.Context) {
	userID := c.GetInt("userID")
	var req struct {
		Name           string  `json:"name"`
		DefaultTaxRate float64 `json:"default_tax_rate"`
		DefaultItems   string  `json:"default_items"`
		Notes          string  `json:"notes"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request"})
		return
	}
	var id int
	db := c.MustGet("db").(*sql.DB)
	err := db.QueryRow(
		"INSERT INTO invoice_templates (user_id, name, default_tax_rate, default_items, notes) VALUES ($1, $2, $3, $4, $5) RETURNING id",
		userID, req.Name, req.DefaultTaxRate, req.DefaultItems, req.Notes,
	).Scan(&id)
	if err != nil {
		c.J<PERSON>(http.StatusInternalServerError, gin.H{"error": "Failed to create template"})
		return
	}
	c.JSON(http.StatusOK, gin.H{"id": id})
}
