package handlers

import (
	"database/sql"
	"fmt"
	"log"
	"net/http"

	"github.com/gin-gonic/gin"
)

func CreateTransactionHandler(c *gin.Context) {
	userID := c.GetInt("userID")
	log.Printf("CreateTransactionHandler called, userID: %v", userID)

	var req struct {
		Amount      float64 `json:"amount"`
		Category    string  `json:"category"`
		Description string  `json:"description"`
		Date        string  `json:"date"`
		Type        string  `json:"type"`
		Currency    string  `json:"currency"`
		Notes       *string `json:"notes"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("BindJSON error: %v", err)
		c.J<PERSON>(http.StatusBadRequest, gin.H{"error": "Invalid request"})
		return
	}
	log.Printf("Request body: %+v", req)

	var id int
	var createdDate string
	db := c.Must<PERSON>et("db").(*sql.DB)
	currency := req.Currency
	if currency == "" {
		currency = "USD"
	}
	err := db.QueryRow(
		"INSERT INTO transactions (user_id, amount, category, description, date, type, currency, notes) VALUES ($1, $2, $3, $4, $5, $6, $7, $8) RETURNING id, date",
		userID, req.Amount, req.Category, req.Description, req.Date, req.Type, currency, req.Notes,
	).Scan(&id, &createdDate)
	if err != nil {
		log.Printf("DB error: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create transaction", "details": err.Error()})
		return
	}
	log.Printf("Transaction created with id: %v", id)

	// Return the complete transaction object
	c.JSON(http.StatusOK, gin.H{
		"id":          fmt.Sprintf("%d", id),
		"amount":      req.Amount,
		"category":    req.Category,
		"description": req.Description,
		"date":        createdDate,
		"type":        req.Type,
		"currency":    currency,
		"notes":       req.Notes,
		"isRecurring": false,
		"recurringId": nil,
	})
}
