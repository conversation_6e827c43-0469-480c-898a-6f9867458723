package handlers

import (
	"database/sql"
	"log"
	"net/http"
	"time"

	"securebooks/utils"

	"golang.org/x/crypto/bcrypt"

	"github.com/gin-gonic/gin"
)

func ResetPasswordHandler(c *gin.Context) {
	var req struct {
		Email string `json:"email"`
	}
	if err := c.ShouldBindJ<PERSON>(&req); err != nil || req.Email == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid email"})
		return
	}
	log.Printf("Password reset requested for email: %s", req.Email)
	db := c.MustGet("db").(*sql.DB)
	var userID int
	err := db.QueryRow("SELECT id FROM users WHERE email = $1", req.Email).Scan(&userID)
	if err == sql.ErrNoRows {
		// Always return generic message
		c.JSON(http.StatusOK, gin.H{"message": "If this email exists, a reset link will be sent."})
		return
	} else if err != nil {
		c.J<PERSON>(http.StatusInternalServerError, gin.H{"error": "Database error"})
		return
	}
	// Generate and store password reset token
	token, err := utils.GenerateRefreshTokenString()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate token"})
		return
	}
	expiresAt := time.Now().Add(1 * time.Hour)
	_, err = db.Exec(
		"INSERT INTO password_reset_tokens (user_id, token, expires_at, created_at, updated_at) VALUES ($1, $2, $3, NOW(), NOW())",
		userID, token, expiresAt,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to store reset token"})
		return
	}
	// TODO: Send email with reset link (e.g., https://yourapp.com/reset-password?token=...)
	log.Printf("Password reset token for user %d: %s", userID, token)
	c.JSON(http.StatusOK, gin.H{"message": "If this email exists, a reset link will be sent."})
}

// POST /api/v1/auth/reset-password/confirm
func ResetPasswordConfirmHandler(c *gin.Context) {
	var req struct {
		Token       string `json:"token"`
		NewPassword string `json:"newPassword"`
	}
	if err := c.ShouldBindJSON(&req); err != nil || req.Token == "" || len(req.NewPassword) < 6 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request"})
		return
	}
	db := c.MustGet("db").(*sql.DB)
	var userID int
	var expiresAt time.Time
	err := db.QueryRow("SELECT user_id, expires_at FROM password_reset_tokens WHERE token = $1 AND deleted_at IS NULL", req.Token).Scan(&userID, &expiresAt)
	if err == sql.ErrNoRows {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid or expired token"})
		return
	} else if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error"})
		return
	}
	if time.Now().After(expiresAt) {
		_, _ = db.Exec("DELETE FROM password_reset_tokens WHERE token = $1", req.Token)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Token expired"})
		return
	}
	// Hash new password
	hash, err := bcrypt.GenerateFromPassword([]byte(req.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to hash password"})
		return
	}
	_, err = db.Exec("UPDATE users SET password_hash = $1 WHERE id = $2", string(hash), userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update password"})
		return
	}
	_, _ = db.Exec("DELETE FROM password_reset_tokens WHERE token = $1", req.Token)
	c.JSON(http.StatusOK, gin.H{"message": "Password has been reset successfully."})
}
