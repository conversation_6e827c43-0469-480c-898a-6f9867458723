import 'package:flutter_test/flutter_test.dart';
import 'package:securebooks/models/transaction.dart';

void main() {
  group('Transaction Model Tests', () {
    test('should create a transaction with all required fields', () {
      final transaction = Transaction(
        id: 1,
        description: 'Test transaction',
        amount: 100.0,
        category: 'Food',
        date: DateTime(2024, 1, 1),
        type: TransactionType.expense,
      );

      expect(transaction.id, equals(1));
      expect(transaction.description, equals('Test transaction'));
      expect(transaction.amount, equals(100.0));
      expect(transaction.category, equals('Food'));
      expect(transaction.date, equals(DateTime(2024, 1, 1)));
      expect(transaction.type, equals(TransactionType.expense));
    });

    test('should convert transaction to JSON correctly', () {
      final transaction = Transaction(
        id: 1,
        description: 'Test transaction',
        amount: 100.0,
        category: 'Food',
        date: DateTime(2024, 1, 1),
        type: TransactionType.expense,
      );

      final json = transaction.toJson();

      expect(json['id'], equals(1));
      expect(json['description'], equals('Test transaction'));
      expect(json['amount'], equals(100.0));
      expect(json['category'], equals('Food'));
      expect(json['type'], equals('expense'));
    });

    test('should create transaction from JSON correctly', () {
      final json = {
        'id': 1,
        'description': 'Test transaction',
        'amount': 100.0,
        'category': 'Food',
        'date': '2024-01-01T00:00:00.000Z',
        'type': 'expense',
      };

      final transaction = Transaction.fromJson(json);

      expect(transaction.id, equals(1));
      expect(transaction.description, equals('Test transaction'));
      expect(transaction.amount, equals(100.0));
      expect(transaction.category, equals('Food'));
      expect(transaction.type, equals(TransactionType.expense));
    });

    test('should handle income transaction type', () {
      final transaction = Transaction(
        id: 2,
        description: 'Salary',
        amount: 5000.0,
        category: 'Income',
        date: DateTime(2024, 1, 1),
        type: TransactionType.income,
      );

      expect(transaction.type, equals(TransactionType.income));
      expect(transaction.toJson()['type'], equals('income'));
    });

    test('should copy transaction with updated fields', () {
      final original = Transaction(
        id: 1,
        description: 'Original',
        amount: 100.0,
        category: 'Food',
        date: DateTime(2024, 1, 1),
        type: TransactionType.expense,
      );

      final copied = original.copyWith(
        description: 'Updated',
        amount: 200.0,
      );

      expect(copied.id, equals(original.id));
      expect(copied.description, equals('Updated'));
      expect(copied.amount, equals(200.0));
      expect(copied.category, equals(original.category));
      expect(copied.date, equals(original.date));
      expect(copied.type, equals(original.type));
    });

    test('should validate required fields', () {
      expect(
        () => Transaction(
          id: 1,
          description: '',
          amount: 100.0,
          category: 'Food',
          date: DateTime(2024, 1, 1),
          type: TransactionType.expense,
        ),
        throwsA(isA<ArgumentError>()),
      );
    });

    test('should validate positive amount', () {
      expect(
        () => Transaction(
          id: 1,
          description: 'Test',
          amount: -100.0,
          category: 'Food',
          date: DateTime(2024, 1, 1),
          type: TransactionType.expense,
        ),
        throwsA(isA<ArgumentError>()),
      );
    });
  });
}
