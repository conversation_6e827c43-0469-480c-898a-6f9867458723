import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fl_chart/fl_chart.dart';
import '../providers/expense_provider.dart';
import '../models/transaction.dart';
import '../utils/theme.dart';
import '../providers/auth_provider.dart';

class ExpenseScreen extends StatefulWidget {
  final VoidCallback? onStateReady;
  const ExpenseScreen({super.key, this.onStateReady});

  @override
  ExpenseScreenState createState() => ExpenseScreenState();
}

class ExpenseScreenState extends State<ExpenseScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.onStateReady != null) widget.onStateReady!();
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      Provider.of<ExpenseProvider>(context, listen: false).loadTransactions(authProvider: authProvider);
    });
  }

  @override
  Widget build(BuildContext context) {
    final provider = Provider.of<ExpenseProvider>(context);
    return Scaffold(
      body: Consumer<ExpenseProvider>(
        builder: (context, expenseProvider, child) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (expenseProvider.error != null && expenseProvider.error!.isNotEmpty) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(expenseProvider.error!),
                  backgroundColor: Colors.red,
                ),
              );
              expenseProvider.clearError();
            }
          });
          if (expenseProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }
          // No duplicate top bar here
          return RefreshIndicator(
            onRefresh: () {
              final authProvider = Provider.of<AuthProvider>(context, listen: false);
              return Provider.of<ExpenseProvider>(context, listen: false).loadTransactions(authProvider: authProvider);
            },
            child: ListView(
              padding: const EdgeInsets.all(16),
              children: [
                // Main content only
                _buildFilters(context, expenseProvider),
                const SizedBox(height: 16),
                _buildSummaryCards(expenseProvider),
                const SizedBox(height: 24),
                _buildCharts(expenseProvider),
                const SizedBox(height: 24),
                _buildTransactionsList(expenseProvider),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildFilters(BuildContext context, ExpenseProvider provider) {
    final categories = provider.categories + provider.incomeCategories;
    return Card(
      elevation: 2,
      margin: EdgeInsets.zero,
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: TextField(
                    decoration: const InputDecoration(
                      hintText: 'Search by description or category',
                      prefixIcon: Icon(Icons.search),
                      border: OutlineInputBorder(),
                      isDense: true,
                    ),
                    onChanged: (value) => provider.searchText = value,
                    controller: TextEditingController(text: provider.searchText),
                  ),
                ),
                const SizedBox(width: 8),
                IconButton(
                  icon: const Icon(Icons.clear),
                  tooltip: 'Clear Filters',
                  onPressed: provider.clearFilters,
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<String?>(
                    value: provider.selectedCategory,
                    decoration: const InputDecoration(
                      labelText: 'Category',
                      border: OutlineInputBorder(),
                      isDense: true,
                    ),
                    items: [
                      const DropdownMenuItem<String?>(value: null, child: Text('All Categories')),
                      ...categories.map((cat) => DropdownMenuItem<String?>(value: cat, child: Text(cat))),
                    ],
                    onChanged: (value) => provider.selectedCategory = value,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: DropdownButtonFormField<String?>(
                    value: provider.selectedType,
                    decoration: const InputDecoration(
                      labelText: 'Type',
                      border: OutlineInputBorder(),
                      isDense: true,
                    ),
                    items: const [
                      DropdownMenuItem<String?>(value: null, child: Text('All Types')),
                      DropdownMenuItem<String?>(value: 'income', child: Text('Income')),
                      DropdownMenuItem<String?>(value: 'expense', child: Text('Expense')),
                    ],
                    onChanged: (value) => provider.selectedType = value,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: OutlinedButton.icon(
                    icon: const Icon(Icons.date_range),
                    label: Text(provider.dateRange == null
                        ? 'Date Range'
                        : '${provider.dateRange!.start.month}/${provider.dateRange!.start.day}/${provider.dateRange!.start.year} - ${provider.dateRange!.end.month}/${provider.dateRange!.end.day}/${provider.dateRange!.end.year}'),
                    onPressed: () async {
                      final picked = await showDateRangePicker(
                        context: context,
                        firstDate: DateTime(2000),
                        lastDate: DateTime.now().add(const Duration(days: 365)),
                        initialDateRange: provider.dateRange,
                      );
                      if (picked != null) {
                        provider.dateRange = picked;
                      }
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCards(ExpenseProvider provider) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 4, // Four cards in a single row
      crossAxisSpacing: 8,
      mainAxisSpacing: 8,
      childAspectRatio: 0.85, // Make cards more square/compact
      children: [
        _buildSummaryCard(
          'Total Balance',
          '\$${provider.totalBalance.toStringAsFixed(2)}',
          provider.totalBalance >= 0 ? Colors.green : Colors.red,
          Icons.account_balance_wallet,
        ),
        _buildSummaryCard(
          'Total Income',
          '\$${provider.totalIncome.toStringAsFixed(2)}',
          Colors.green,
          Icons.trending_up,
        ),
        _buildSummaryCard(
          'Total Expenses',
          '\$${provider.totalExpenses.toStringAsFixed(2)}',
          Colors.red,
          Icons.trending_down,
        ),
        _buildSummaryCard(
          'Transactions',
          provider.transactions.length.toString(), // No dollar sign
          AppTheme.primaryColor,
          Icons.receipt,
        ),
      ],
    );
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    final provider = Provider.of<ExpenseProvider>(context, listen: false);
    final currency = provider.filteredTransactions.isNotEmpty
        ? provider.filteredTransactions.first.currency
        : 'USD';
    final symbol = getCurrencySymbol(currency);
    return SizedBox(
      height: 36, // Half the previous height
      child: Card(
        elevation: 4,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 4),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Icon(icon, color: color, size: 28),
              const SizedBox(height: 2),
              Text(
                '$symbol$value',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: color,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              Text(
                title,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                  fontSize: 12,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCharts(ExpenseProvider provider) {
    // Separate category totals for income and expenses
    final expenseCategoryTotals = <String, double>{};
    final incomeCategoryTotals = <String, double>{};
    for (var t in provider.transactions) {
      if (t.type == 'expense') {
        expenseCategoryTotals[t.category] = (expenseCategoryTotals[t.category] ?? 0) + t.amount;
      } else if (t.type == 'income') {
        incomeCategoryTotals[t.category] = (incomeCategoryTotals[t.category] ?? 0) + t.amount;
      }
    }
    return LayoutBuilder(
      builder: (context, constraints) {
        final isWide = constraints.maxWidth > 900;
        final hasExpenses = expenseCategoryTotals.isNotEmpty;
        final hasIncome = incomeCategoryTotals.isNotEmpty;
        Widget expenseChart = _buildPieChartWithMessage(
          hasExpenses ? _buildPieChartSections(expenseCategoryTotals) : [],
          'Expenses by Category',
          hasExpenses ? null : 'No data to display',
        );
        Widget incomeChart = _buildPieChartWithMessage(
          hasIncome ? _buildPieChartSections(incomeCategoryTotals) : [],
          'Income by Category',
          hasIncome ? null : 'No data to display',
        );
        Widget expenseList = _buildCategoryList(expenseCategoryTotals, 'Top Expense Categories');
        Widget incomeList = _buildCategoryList(incomeCategoryTotals, 'Top Income Categories');
        final expenseBlock = isWide
            ? Expanded(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(child: Center(child: expenseChart)),
                    const SizedBox(width: 16),
                    SizedBox(width: 220, child: Center(child: expenseList)),
                  ],
                ),
              )
            : Column(children: [Center(child: expenseChart), const SizedBox(height: 8), Center(child: expenseList)]);
        final incomeBlock = isWide
            ? Expanded(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(child: Center(child: incomeChart)),
                    const SizedBox(width: 16),
                    SizedBox(width: 220, child: Center(child: incomeList)),
                  ],
                ),
              )
            : Column(children: [Center(child: incomeChart), const SizedBox(height: 8), Center(child: incomeList)]);
        return isWide
            ? Row(crossAxisAlignment: CrossAxisAlignment.start, mainAxisAlignment: MainAxisAlignment.center, children: [expenseBlock, const SizedBox(width: 24), incomeBlock])
            : Column(children: [expenseBlock, const SizedBox(height: 24), incomeBlock]);
      },
    );
  }

  Widget _buildPieChartWithMessage(List<PieChartSectionData> sections, String label, String? message) {
    return Column(
      children: [
        Text(label, style: const TextStyle(fontWeight: FontWeight.bold)),
        SizedBox(
          height: 200,
          child: Stack(
            alignment: Alignment.center,
            children: [
              PieChart(
                PieChartData(
                  sections: sections.isNotEmpty
                      ? sections
                      : [
                          PieChartSectionData(
                            value: 1,
                            color: Colors.grey[300],
                            title: '',
                            radius: 60,
                          ),
                        ],
                  centerSpaceRadius: 40,
                ),
              ),
              if (message != null)
                Center(
                  child: Text(
                    message,
                    style: const TextStyle(color: Colors.grey, fontWeight: FontWeight.bold),
                    textAlign: TextAlign.center,
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }

  List<PieChartSectionData> _buildPieChartSections(Map<String, double> categoryTotals) {
    final colors = [
      Colors.blue,
      Colors.red,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.teal,
      Colors.pink,
      Colors.indigo,
    ];
    return categoryTotals.entries.map((entry) {
      final index = categoryTotals.keys.toList().indexOf(entry.key);
      return PieChartSectionData(
        value: entry.value,
        title: '${entry.key}\n\$${entry.value.toStringAsFixed(0)}',
        color: colors[index % colors.length],
        radius: 60,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      );
    }).toList();
  }

  Widget _buildCategoryList(Map<String, double> categoryTotals, String label) {
    if (categoryTotals.isEmpty) {
      return const SizedBox.shrink();
    }
    final total = categoryTotals.values.fold(0.0, (a, b) => a + b);
    final sorted = categoryTotals.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    final top10 = sorted.take(10).toList();
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(label, style: const TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            ...top10.map((entry) {
              final percent = total > 0 ? (entry.value / total * 100) : 0;
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 2),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(child: Text(entry.key, overflow: TextOverflow.ellipsis)),
                    Text('${percent.toStringAsFixed(1)}%'),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionsList(ExpenseProvider provider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Recent Transactions',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            TextButton(
              onPressed: () => _showAddRecurringDialog(context),
              child: const Text('Add Recurring'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: provider.filteredTransactions.length,
          itemBuilder: (context, index) {
            final transaction = provider.filteredTransactions[index];
            return _buildTransactionItem(transaction, provider);
          },
        ),
      ],
    );
  }

  Widget _buildTransactionItem(Transaction transaction, ExpenseProvider provider) {
    final symbol = getCurrencySymbol(transaction.currency);
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: transaction.type == 'income' ? Colors.green : Colors.red,
          child: Icon(
            transaction.type == 'income' ? Icons.trending_up : Icons.trending_down,
            color: Colors.white,
          ),
        ),
        title: Text(transaction.description),
        subtitle: Text('${transaction.category} • ${_formatDate(transaction.date)}'),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              '$symbol${transaction.amount.toStringAsFixed(2)}',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: transaction.type == 'income' ? Colors.green : Colors.red,
              ),
            ),
            if (transaction.isRecurring)
              const Text(
                'Recurring',
                style: TextStyle(fontSize: 12, color: Colors.blue),
              ),
          ],
        ),
        onTap: () => _showEditTransactionDialog(context, transaction, provider),
      ),
    );
  }

  String _formatDate(dynamic date) {
    DateTime dt;
    if (date is DateTime) {
      dt = date;
    } else if (date is String) {
      dt = DateTime.tryParse(date) ?? DateTime.now();
    } else {
      return '';
    }
    return '${dt.month}/${dt.day}/${dt.year}';
  }

  void showAddTransactionDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const AddTransactionDialog(),
    );
  }

  void _showAddRecurringDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const AddRecurringDialog(),
    );
  }

  void _showEditTransactionDialog(BuildContext context, Transaction transaction, ExpenseProvider provider) {
    showDialog(
      context: context,
      builder: (context) => EditTransactionDialog(transaction: transaction, provider: provider),
    );
  }

  List<Widget> buildAppBarActions(BuildContext context) {
    return [
      ElevatedButton.icon(
        icon: const Icon(Icons.add),
        label: const Text('New Transaction'),
        onPressed: () {
          showAddTransactionDialog(context);
        },
      ),
    ];
  }
}

class AddTransactionDialog extends StatefulWidget {
  const AddTransactionDialog({super.key});

  @override
  State<AddTransactionDialog> createState() => _AddTransactionDialogState();
}

class _AddTransactionDialogState extends State<AddTransactionDialog> {
  final _formKey = GlobalKey<FormState>();
  final _descriptionController = TextEditingController();
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();
  String _selectedType = 'expense';
  String _selectedCategory = 'Food & Dining';
  final DateTime _selectedDate = DateTime.now();
  String _selectedCurrency = 'USD';

  static const List<String> _currencies = [
    'USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD', 'INR', 'CNY', 'BRL', 'ZAR'
  ];

  @override
  void dispose() {
    _descriptionController.dispose();
    _amountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final expenseProvider = Provider.of<ExpenseProvider>(context, listen: false);
    final categories = _selectedType == 'income' 
        ? expenseProvider.incomeCategories 
        : expenseProvider.categories;

    return AlertDialog(
      title: const Text('Add Transaction'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              DropdownButtonFormField<String>(
                value: _selectedType,
                decoration: const InputDecoration(labelText: 'Type'),
                items: const [
                  DropdownMenuItem(value: 'income', child: Text('Income')),
                  DropdownMenuItem(value: 'expense', child: Text('Expense')),
                ],
                onChanged: (value) {
                  setState(() {
                    _selectedType = value!;
                    _selectedCategory = value == 'income' 
                        ? expenseProvider.incomeCategories.first 
                        : expenseProvider.categories.first;
                  });
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedCurrency,
                decoration: const InputDecoration(labelText: 'Currency'),
                items: _currencies.map((c) => DropdownMenuItem(value: c, child: Text(c))).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedCurrency = value!;
                  });
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(labelText: 'Description'),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a description';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _amountController,
                decoration: const InputDecoration(labelText: 'Amount'),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Amount is required';
                  }
                  final amount = double.tryParse(value);
                  if (amount == null || amount <= 0) {
                    return 'Invalid amount';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedCategory,
                decoration: const InputDecoration(labelText: 'Category'),
                items: categories.map((category) {
                  return DropdownMenuItem(value: category, child: Text(category));
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedCategory = value!;
                  });
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _notesController,
                decoration: const InputDecoration(labelText: 'Notes (Optional)'),
                maxLines: 3,
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () => _saveTransaction(expenseProvider),
          child: const Text('Save'),
        ),
      ],
    );
  }

  void _saveTransaction(ExpenseProvider provider) {
    if (!_formKey.currentState!.validate()) return;

    final transaction = Transaction(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      description: _descriptionController.text,
      amount: double.parse(_amountController.text),
      category: _selectedCategory,
      type: _selectedType,
      date: _selectedDate,
      notes: _notesController.text.isEmpty ? null : _notesController.text,
      currency: _selectedCurrency,
    );

    print('Creating transaction: type=${transaction.type}, amount=${transaction.amount}, category=${transaction.category}, description=${transaction.description}, date=${transaction.date}, currency=${transaction.currency}');

    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    provider.addTransaction(transaction, authProvider: authProvider, context: context);
    Navigator.of(context).pop();
  }
}

class AddRecurringDialog extends StatefulWidget {
  const AddRecurringDialog({super.key});

  @override
  State<AddRecurringDialog> createState() => _AddRecurringDialogState();
}

class _AddRecurringDialogState extends State<AddRecurringDialog> {
  final _formKey = GlobalKey<FormState>();
  final _descriptionController = TextEditingController();
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();
  String _selectedType = 'expense';
  String _selectedCategory = 'Food & Dining';
  String _selectedFrequency = 'monthly';
  DateTime _selectedStartDate = DateTime.now();
  DateTime? _selectedEndDate;

  @override
  void dispose() {
    _descriptionController.dispose();
    _amountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final expenseProvider = Provider.of<ExpenseProvider>(context, listen: false);
    final categories = _selectedType == 'income'
        ? expenseProvider.incomeCategories
        : expenseProvider.categories;
    return AlertDialog(
      title: const Text('Add Recurring Transaction'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              DropdownButtonFormField<String>(
                value: _selectedType,
                decoration: const InputDecoration(labelText: 'Type'),
                items: const [
                  DropdownMenuItem(value: 'income', child: Text('Income')),
                  DropdownMenuItem(value: 'expense', child: Text('Expense')),
                ],
                onChanged: (value) {
                  setState(() {
                    _selectedType = value!;
                    _selectedCategory = value == 'income'
                        ? expenseProvider.incomeCategories.first
                        : expenseProvider.categories.first;
                  });
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(labelText: 'Description'),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a description';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _amountController,
                decoration: const InputDecoration(labelText: 'Amount'),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Amount is required';
                  }
                  final amount = double.tryParse(value);
                  if (amount == null || amount <= 0) {
                    return 'Invalid amount';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedCategory,
                decoration: const InputDecoration(labelText: 'Category'),
                items: categories.map((category) {
                  return DropdownMenuItem(value: category, child: Text(category));
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedCategory = value!;
                  });
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedFrequency,
                decoration: const InputDecoration(labelText: 'Frequency'),
                items: const [
                  DropdownMenuItem(value: 'weekly', child: Text('Weekly')),
                  DropdownMenuItem(value: 'monthly', child: Text('Monthly')),
                  DropdownMenuItem(value: 'yearly', child: Text('Yearly')),
                ],
                onChanged: (value) {
                  setState(() {
                    _selectedFrequency = value!;
                  });
                },
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  const Text('Start Date:'),
                  const SizedBox(width: 8),
                  Expanded(
                    child: TextButton(
                      onPressed: () async {
                        final picked = await showDatePicker(
                          context: context,
                          initialDate: _selectedStartDate,
                          firstDate: DateTime(2000),
                          lastDate: DateTime(2100),
                        );
                        if (picked != null) {
                          setState(() {
                            _selectedStartDate = picked;
                          });
                        }
                      },
                      child: Text('${_selectedStartDate.year}-${_selectedStartDate.month.toString().padLeft(2, '0')}-${_selectedStartDate.day.toString().padLeft(2, '0')}'),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  const Text('End Date:'),
                  const SizedBox(width: 8),
                  Expanded(
                    child: TextButton(
                      onPressed: () async {
                        final picked = await showDatePicker(
                          context: context,
                          initialDate: _selectedEndDate ?? _selectedStartDate,
                          firstDate: _selectedStartDate,
                          lastDate: DateTime(2100),
                        );
                        if (picked != null) {
                          setState(() {
                            _selectedEndDate = picked;
                          });
                        }
                      },
                      child: Text(_selectedEndDate == null
                          ? 'None'
                          : '${_selectedEndDate!.year}-${_selectedEndDate!.month.toString().padLeft(2, '0')}-${_selectedEndDate!.day.toString().padLeft(2, '0')}'),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: () {
                      setState(() {
                        _selectedEndDate = null;
                      });
                    },
                  ),
                ],
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _notesController,
                decoration: const InputDecoration(labelText: 'Notes (Optional)'),
                maxLines: 3,
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () => _saveRecurring(expenseProvider),
          child: const Text('Save'),
        ),
      ],
    );
  }

  void _saveRecurring(ExpenseProvider provider) {
    if (!_formKey.currentState!.validate()) return;
    final recurring = RecurringTransaction(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      description: _descriptionController.text,
      amount: double.parse(_amountController.text),
      category: _selectedCategory,
      type: _selectedType,
      frequency: _selectedFrequency,
      startDate: _selectedStartDate, // DateTime
      endDate: _selectedEndDate, // DateTime or null
      notes: _notesController.text.isEmpty ? null : _notesController.text,
    );
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    provider.addRecurringTransaction(recurring, authProvider: authProvider, context: context);
    Navigator.of(context).pop();
  }
} 

class EditTransactionDialog extends StatefulWidget {
  final Transaction transaction;
  final ExpenseProvider provider;
  const EditTransactionDialog({super.key, required this.transaction, required this.provider});

  @override
  State<EditTransactionDialog> createState() => _EditTransactionDialogState();
}

class _EditTransactionDialogState extends State<EditTransactionDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _descriptionController;
  late TextEditingController _amountController;
  late TextEditingController _notesController;
  late String _selectedType;
  late String _selectedCategory;
  late DateTime _selectedDate;
  late String _selectedCurrency;

  static const List<String> _currencies = [
    'USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD', 'INR', 'CNY', 'BRL', 'ZAR'
  ];

  @override
  void initState() {
    super.initState();
    _descriptionController = TextEditingController(text: widget.transaction.description);
    _amountController = TextEditingController(text: widget.transaction.amount.toString());
    _notesController = TextEditingController(text: widget.transaction.notes ?? '');
    _selectedType = widget.transaction.type;
    _selectedCategory = widget.transaction.category;
    _selectedDate = widget.transaction.date is DateTime ? widget.transaction.date : DateTime.tryParse(widget.transaction.date.toString()) ?? DateTime.now();
    _selectedCurrency = widget.transaction.currency;
  }

  @override
  void dispose() {
    _descriptionController.dispose();
    _amountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final categories = _selectedType == 'income'
        ? widget.provider.incomeCategories
        : widget.provider.categories;
    return AlertDialog(
      title: const Text('Edit Transaction'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              DropdownButtonFormField<String>(
                value: _selectedType,
                decoration: const InputDecoration(labelText: 'Type'),
                items: const [
                  DropdownMenuItem(value: 'income', child: Text('Income')),
                  DropdownMenuItem(value: 'expense', child: Text('Expense')),
                ],
                onChanged: (value) {
                  setState(() {
                    _selectedType = value!;
                    _selectedCategory = value == 'income'
                        ? widget.provider.incomeCategories.first
                        : widget.provider.categories.first;
                  });
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedCurrency,
                decoration: const InputDecoration(labelText: 'Currency'),
                items: _currencies.map((c) => DropdownMenuItem(value: c, child: Text(c))).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedCurrency = value!;
                  });
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(labelText: 'Description'),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a description';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _amountController,
                decoration: const InputDecoration(labelText: 'Amount'),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Amount is required';
                  }
                  final amount = double.tryParse(value);
                  if (amount == null || amount <= 0) {
                    return 'Invalid amount';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedCategory,
                decoration: const InputDecoration(labelText: 'Category'),
                items: categories.map((category) {
                  return DropdownMenuItem(value: category, child: Text(category));
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedCategory = value!;
                  });
                },
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  const Text('Date:'),
                  const SizedBox(width: 8),
                  Expanded(
                    child: TextButton(
                      onPressed: () async {
                        final picked = await showDatePicker(
                          context: context,
                          initialDate: _selectedDate,
                          firstDate: DateTime(2000),
                          lastDate: DateTime(2100),
                        );
                        if (picked != null) {
                          setState(() {
                            _selectedDate = picked;
                          });
                        }
                      },
                      child: Text('${_selectedDate.year}-${_selectedDate.month.toString().padLeft(2, '0')}-${_selectedDate.day.toString().padLeft(2, '0')}'),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _notesController,
                decoration: const InputDecoration(labelText: 'Notes (Optional)'),
                maxLines: 3,
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _saveEdit,
          child: const Text('Save'),
        ),
      ],
    );
  }

  void _saveEdit() {
    if (!_formKey.currentState!.validate()) return;
    final updatedTransaction = widget.transaction.copyWith(
      description: _descriptionController.text,
      amount: double.parse(_amountController.text),
      category: _selectedCategory,
      type: _selectedType,
      date: _selectedDate, // Pass as DateTime
      notes: _notesController.text.isEmpty ? null : _notesController.text,
      currency: _selectedCurrency,
    );
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    widget.provider.updateTransaction(updatedTransaction, authProvider: authProvider, context: context);
    Navigator.of(context).pop();
  }
} 

String getCurrencySymbol(String code) {
  switch (code) {
    case 'USD': return '\$';
    case 'EUR': return '€';
    case 'GBP': return '£';
    case 'JPY': return '¥';
    case 'CAD': return 'C\$';
    case 'AUD': return 'A\$';
    case 'INR': return '₹';
    case 'CNY': return '¥';
    case 'BRL': return 'R\$';
    case 'ZAR': return 'R';
    default: return code;
  }
}