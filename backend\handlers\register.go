package handlers

import (
	"database/sql"
	"fmt"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"golang.org/x/crypto/bcrypt"

	"securebooks/utils"
)

// GenerateJWT generates a JWT for a given user ID
func GenerateJWT(userID int) (string, error) {
	secret := os.Getenv("JWT_SECRET")
	if secret == "" {
		return "", fmt.<PERSON><PERSON><PERSON>("JWT_SECRET not set")
	}
	claims := jwt.MapClaims{
		"user_id": userID,
		"exp":     time.Now().Add(72 * time.Hour).Unix(),
	}
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(secret))
}

func RegisterHandler(c *gin.Context) {
	var req struct {
		Email    string `json:"email"`
		Password string `json:"password"`
		Name     string `json:"name"`
	}
	if err := c.<PERSON>(&req); err != nil {
		c.J<PERSON>(http.StatusBadRequest, gin.H{"error": "Invalid request"})
		return
	}
	fieldErrors := gin.H{}
	if len(req.Password) < 6 {
		fieldErrors["password"] = "Password must be at least 6 characters"
	}
	if len(req.Email) == 0 || !strings.Contains(req.Email, "@") {
		fieldErrors["email"] = "Invalid email address"
	}
	if len(fieldErrors) > 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":        "Registration failed",
			"field_errors": fieldErrors,
		})
		return
	}
	// Check if email already exists
	var exists bool
	db := c.MustGet("db").(*sql.DB)
	err := db.QueryRow("SELECT EXISTS(SELECT 1 FROM users WHERE email=$1)", req.Email).Scan(&exists)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error"})
		return
	}
	if exists {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":        "Registration failed",
			"field_errors": gin.H{"email": "Email already in use"},
		})
		return
	}
	// Hash password
	hash, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to hash password"})
		return
	}
	// Insert user
	var userID int
	err = db.QueryRow(
		"INSERT INTO users (email, password_hash, name) VALUES ($1, $2, $3) RETURNING id",
		req.Email, string(hash), req.Name,
	).Scan(&userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create user"})
		return
	}
	jwtToken, err := GenerateJWT(userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate token"})
		return
	}
	// Generate and store refresh token
	refreshToken, err := utils.GenerateRefreshTokenString()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate refresh token"})
		return
	}
	expiresAt := time.Now().Add(30 * 24 * time.Hour) // 30 days
	_, err = db.Exec(
		"INSERT INTO refresh_tokens (user_id, token, expires_at, created_at, updated_at) VALUES ($1, $2, $3, NOW(), NOW())",
		userID, refreshToken, expiresAt,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to store refresh token"})
		return
	}
	c.JSON(http.StatusOK, gin.H{"token": jwtToken, "refreshToken": refreshToken, "userId": userID})
}
