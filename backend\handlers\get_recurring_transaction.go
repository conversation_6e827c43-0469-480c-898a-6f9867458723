package handlers

import (
	"database/sql"
	"net/http"

	"github.com/gin-gonic/gin"
)

func GetRecurringTransactionHandler(c *gin.Context) {
	userID := c.GetInt("userID")
	id := c.<PERSON>("id")
	var r struct {
		ID          int
		Description string
		Amount      float64
		Category    string
		Type        string
		Frequency   string
		StartDate   string
		EndDate     string
		Notes       string
		CreatedAt   string
	}
	db := c.MustGet("db").(*sql.DB)
	err := db.QueryRow("SELECT id, description, amount, category, type, frequency, start_date, end_date, notes, created_at FROM recurring_transactions WHERE id = $1 AND user_id = $2", id, userID).Scan(&r.ID, &r.Description, &r.Amount, &r.Category, &r.Type, &r.Frequency, &r.StartDate, &r.EndDate, &r.Notes, &r.CreatedAt)
	if err == sql.ErrNoRows {
		c.<PERSON>(http.StatusNotFound, gin.H{"error": "Recurring transaction not found"})
		return
	} else if err != nil {
		c.<PERSON>(http.StatusInternalServerError, gin.H{"error": "Database error"})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"id":          r.ID,
		"description": r.Description,
		"amount":      r.Amount,
		"category":    r.Category,
		"type":        r.Type,
		"frequency":   r.Frequency,
		"start_date":  r.StartDate,
		"end_date":    r.EndDate,
		"notes":       r.Notes,
		"created_at":  r.CreatedAt,
	})
}
