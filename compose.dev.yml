version: "3.9"

services:
  # ====================
  # DEVELOPMENT BACKEND
  # ====================
  backend:
    build: 
      context: ./backend
      dockerfile: ../Dockerfile
    container_name: securebooks_backend_dev
    restart: always
    ports:
      - "8080:8080"
      - "443:443"
      - "80:80"
    env_file:
      - .env.dev
    volumes:
      - /etc/letsencrypt:/etc/letsencrypt:ro
      - ./web/build:/app/web:ro
    depends_on:
      - postgres_dev
    networks:
      - securebooks_network

  # ====================
  # DEVELOPMENT DATABASE (also used for testing)
  # ====================
  postgres_dev:
    image: postgres:17
    container_name: securebooks_db_dev
    restart: always
    env_file:
      - .env.dev
    environment:
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_DB: ${DB_NAME}
    ports:
      - "${DB_PORT}:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
    networks:
      - securebooks_network

volumes:
  postgres_dev_data:

networks:
  securebooks_network:
    driver: bridge