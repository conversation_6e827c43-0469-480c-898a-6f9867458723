import 'package:http/http.dart' as http;

void main() async {
  const baseUrl = 'http://localhost:8080/api/v1';
  
  // Test health endpoint
  print('Testing health endpoint...');
  try {
    final healthResponse = await http.get(Uri.parse('$baseUrl/../health'));
    print('Health status: ${healthResponse.statusCode}');
    print('Health body: ${healthResponse.body}');
  } catch (e) {
    print('Health check failed: $e');
  }
  
  // Test templates endpoint without auth (should fail)
  print('\nTesting templates endpoint without auth...');
  try {
    final response = await http.get(Uri.parse('$baseUrl/templates/'));
    print('Templates status: ${response.statusCode}');
    print('Templates body: ${response.body}');
  } catch (e) {
    print('Templates check failed: $e');
  }
} 