class Transaction {
  final String id;
  final String description;
  final double amount;
  final String category;
  final DateTime date;
  final String type; // 'income' or 'expense'
  final String? notes;
  final bool isRecurring;
  final String? recurringId;
  final String currency;

  Transaction({
    required this.id,
    required this.description,
    required this.amount,
    required this.category,
    required this.date,
    required this.type,
    this.notes,
    this.isRecurring = false,
    this.recurringId,
    this.currency = 'USD',
  });

  factory Transaction.fromJson(Map<String, dynamic> json) {
    return Transaction(
      id: json['id'],
      description: json['description'],
      amount: json['amount'].toDouble(),
      category: json['category'],
      date: DateTime.parse(json['date']),
      type: json['type'],
      notes: json['notes'],
      isRecurring: json['isRecurring'] ?? false,
      recurringId: json['recurringId'],
      currency: json['currency'] ?? 'USD',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'description': description,
      'amount': amount,
      'category': category,
      'date': date.toIso8601String(),
      'type': type,
      'notes': notes,
      'isRecurring': isRecurring,
      'recurringId': recurringId,
      'currency': currency,
    };
  }

  Transaction copyWith({
    String? id,
    String? description,
    double? amount,
    String? category,
    DateTime? date,
    String? type,
    String? notes,
    bool? isRecurring,
    String? recurringId,
    String? currency,
  }) {
    return Transaction(
      id: id ?? this.id,
      description: description ?? this.description,
      amount: amount ?? this.amount,
      category: category ?? this.category,
      date: date ?? this.date,
      type: type ?? this.type,
      notes: notes ?? this.notes,
      isRecurring: isRecurring ?? this.isRecurring,
      recurringId: recurringId ?? this.recurringId,
      currency: currency ?? this.currency,
    );
  }
}

class RecurringTransaction {
  final String id;
  final String description;
  final double amount;
  final String category;
  final String type;
  final String frequency; // 'weekly', 'monthly', 'yearly'
  final DateTime startDate;
  final DateTime? endDate;
  final String? notes;
  final String currency;

  RecurringTransaction({
    required this.id,
    required this.description,
    required this.amount,
    required this.category,
    required this.type,
    required this.frequency,
    required this.startDate,
    this.endDate,
    this.notes,
    this.currency = 'USD',
  });

  factory RecurringTransaction.fromJson(Map<String, dynamic> json) {
    return RecurringTransaction(
      id: json['id'],
      description: json['description'],
      amount: json['amount'].toDouble(),
      category: json['category'],
      type: json['type'],
      frequency: json['frequency'],
      startDate: DateTime.parse(json['startDate']),
      endDate: json['endDate'] != null ? DateTime.parse(json['endDate']) : null,
      notes: json['notes'],
      currency: json['currency'] ?? 'USD',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'description': description,
      'amount': amount,
      'category': category,
      'type': type,
      'frequency': frequency,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'notes': notes,
      'currency': currency,
    };
  }
} 