import 'dart:convert';
import 'package:http/http.dart' as http;
import '../../providers/auth_provider.dart';

class AnalyticsApi {
  static const String baseUrl = 'http://localhost:8080/api/v1/analytics';

  Future<Map<String, String>> _getHeaders() async {
    final token = await AuthProvider.getToken();
    return {
      'Content-Type': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }

  Future<List<Map<String, dynamic>>> fetchSpendingPatterns() async {
    final headers = await _getHeaders();
    final response = await http.get(Uri.parse('$baseUrl/spending-patterns'), headers: headers);
    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return List<Map<String, dynamic>>.from(data['categories']);
    } else {
      throw Exception('Failed to fetch spending patterns');
    }
  }

  Future<Map<String, dynamic>> fetchForecast() async {
    final headers = await _getHeaders();
    final response = await http.get(Uri.parse('$baseUrl/forecast'), headers: headers);
    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('Failed to fetch forecast');
    }
  }

  Future<List<Map<String, dynamic>>> fetchAnomalies() async {
    final headers = await _getHeaders();
    final response = await http.get(Uri.parse('$baseUrl/anomalies'), headers: headers);
    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return List<Map<String, dynamic>>.from(data['anomalies']);
    } else {
      throw Exception('Failed to fetch anomalies');
    }
  }

  Future<Map<String, dynamic>> fetchHealthScore() async {
    final headers = await _getHeaders();
    final response = await http.get(Uri.parse('$baseUrl/health-score'), headers: headers);
    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('Failed to fetch health score');
    }
  }

  Future<Map<String, dynamic>> fetchSavings() async {
    final headers = await _getHeaders();
    final response = await http.get(Uri.parse('$baseUrl/savings'), headers: headers);
    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('Failed to fetch savings');
    }
  }
} 