package handlers

import (
	"database/sql"
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
)

func GetTransactionsHandler(c *gin.Context) {
	userID := c.GetInt("userID")
	fmt.Printf("GetTransactionsHandler: userID = %d\n", userID)

	db, exists := c.Get("db")
	if !exists {
		fmt.Println("GetTransactionsHandler: db not found in context")
		c.<PERSON>(http.StatusInternalServerError, gin.H{"error": "Database connection not found"})
		return
	}

	dbConn, ok := db.(*sql.DB)
	if !ok {
		fmt.Println("GetTransactionsHandler: db is not *sql.DB")
		c.<PERSON>(http.StatusInternalServerError, gin.H{"error": "Invalid database connection"})
		return
	}

	// First, let's test if we can connect to the database at all
	var err error
	err = dbConn.Ping()
	if err != nil {
		fmt.Printf("GetTransactionsHandler: database ping error: %v\n", err)
		c.<PERSON>(http.StatusInternalServerError, gin.H{"error": "Database connection error"})
		return
	}

	// Check if the transactions table exists and has data
	var count int
	err = dbConn.QueryRow("SELECT COUNT(*) FROM transactions WHERE user_id = $1", userID).Scan(&count)
	if err != nil {
		fmt.Printf("GetTransactionsHandler: count query error: %v\n", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database count error"})
		return
	}
	fmt.Printf("GetTransactionsHandler: found %d transactions for user %d\n", count, userID)

	// Let's check what columns exist in the transactions table
	rows, err := dbConn.Query("SELECT column_name FROM information_schema.columns WHERE table_name = 'transactions' ORDER BY ordinal_position")
	if err != nil {
		fmt.Printf("GetTransactionsHandler: schema query error: %v\n", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Schema query error"})
		return
	}
	defer rows.Close()

	fmt.Print("GetTransactionsHandler: Available columns: ")
	for rows.Next() {
		var columnName string
		if err := rows.Scan(&columnName); err != nil {
			fmt.Printf("Error scanning column name: %v\n", err)
			continue
		}
		fmt.Printf("%s ", columnName)
	}
	fmt.Println()

	// Now let's try a simpler query with just the basic columns
	rows, err = dbConn.Query("SELECT id, amount, category, description, date, type FROM transactions WHERE user_id = $1 ORDER BY date DESC", userID)
	if err != nil {
		fmt.Printf("GetTransactionsHandler: database query error: %v\n", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error"})
		return
	}
	defer rows.Close()

	transactions := []gin.H{}
	for rows.Next() {
		var t struct {
			ID          int
			Amount      float64
			Category    string
			Description string
			Date        string
			Type        string
		}
		err := rows.Scan(&t.ID, &t.Amount, &t.Category, &t.Description, &t.Date, &t.Type)
		if err != nil {
			fmt.Printf("GetTransactionsHandler: scan error: %v\n", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Database scan error"})
			return
		}
		transactions = append(transactions, gin.H{
			"id":          fmt.Sprintf("%d", t.ID),
			"amount":      t.Amount,
			"category":    t.Category,
			"description": t.Description,
			"date":        t.Date,
			"type":        t.Type,
			"notes":       nil,
			"isRecurring": false,
			"recurringId": nil,
		})
	}
	c.JSON(http.StatusOK, gin.H{"transactions": transactions})
}
