package handlers

import (
	"database/sql"
	"fmt"
	"net/http"
	"time"

	"securebooks/models"

	"github.com/gin-gonic/gin"
)

type SyncTransactionRequest struct {
	Transactions []models.Transaction `json:"transactions"`
}

type SyncTransactionResponse struct {
	SyncedCount int      `json:"synced_count"`
	Conflicts   []string `json:"conflicts,omitempty"`
	Errors      []string `json:"errors,omitempty"`
}

func SyncTransactionsHandler(c *gin.Context) {
	var req SyncTransactionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	db := c.MustGet("db").(*sql.DB)
	userID := c.MustGet("userID").(int)

	response := SyncTransactionResponse{
		SyncedCount: 0,
		Conflicts:   []string{},
		Errors:      []string{},
	}

	for _, transaction := range req.Transactions {
		// Check if transaction already exists
		var existingID int
		err := db.QueryRow(
			"SELECT id FROM transactions WHERE id = ? AND user_id = ?",
			transaction.ID, userID,
		).Scan(&existingID)

		if err == sql.ErrNoRows {
			// New transaction - insert it
			_, err = db.Exec(`
				INSERT INTO transactions (id, user_id, description, amount, category, type, date, created_at, updated_at)
				VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
			`, transaction.ID, userID, transaction.Description, transaction.Amount, transaction.Category, transaction.Type, transaction.Date, time.Now(), time.Now())

			if err != nil {
				response.Errors = append(response.Errors, "Failed to insert transaction "+fmt.Sprintf("%d", transaction.ID)+": "+err.Error())
			} else {
				response.SyncedCount++
			}
		} else if err != nil {
			response.Errors = append(response.Errors, "Database error for transaction "+fmt.Sprintf("%d", transaction.ID)+": "+err.Error())
		} else {
			// Transaction exists - check if it needs updating
			var lastModified time.Time
			err = db.QueryRow(
				"SELECT updated_at FROM transactions WHERE id = ? AND user_id = ?",
				transaction.ID, userID,
			).Scan(&lastModified)

			if err != nil {
				response.Errors = append(response.Errors, "Failed to get last modified for transaction "+fmt.Sprintf("%d", transaction.ID)+": "+err.Error())
				continue
			}

			// If the incoming transaction is newer, update it
			if transaction.UpdatedAt.After(lastModified) {
				_, err = db.Exec(`
					UPDATE transactions 
					SET description = ?, amount = ?, category = ?, type = ?, date = ?, updated_at = ?
					WHERE id = ? AND user_id = ?
				`, transaction.Description, transaction.Amount, transaction.Category, transaction.Type, transaction.Date, time.Now(), transaction.ID, userID)

				if err != nil {
					response.Errors = append(response.Errors, "Failed to update transaction "+fmt.Sprintf("%d", transaction.ID)+": "+err.Error())
				} else {
					response.SyncedCount++
				}
			} else {
				response.Conflicts = append(response.Conflicts, "Transaction "+fmt.Sprintf("%d", transaction.ID)+" has conflicts (local version is newer)")
			}
		}
	}

	c.JSON(http.StatusOK, response)
}
