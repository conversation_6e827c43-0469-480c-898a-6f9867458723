package handlers

import (
	"database/sql"
	"net/http"

	"github.com/gin-gonic/gin"
)

func GetSummaryHandler(c *gin.Context) {
	userID := c.GetInt("userID")
	var totalIncome, totalExpense float64
	db := c.MustGet("db").(*sql.DB)
	_ = db.QueryRow("SELECT COALESCE(SUM(amount),0) FROM transactions WHERE user_id = $1 AND type = 'income'", userID).Scan(&totalIncome)
	_ = db.QueryRow("SELECT COALESCE(SUM(amount),0) FROM transactions WHERE user_id = $1 AND type = 'expense'", userID).Scan(&totalExpense)
	c.<PERSON>(http.StatusOK, gin.H{
		"total_income":  totalIncome,
		"total_expense": totalExpense,
		"balance":       totalIncome - totalExpense,
	})
}
