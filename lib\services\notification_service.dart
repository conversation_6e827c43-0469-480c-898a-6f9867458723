import 'package:flutter/material.dart';
import '../widgets/notification_overlay.dart';

class NotificationService {
  static void showSuccessNotification(BuildContext context, String message, {String? title}) {
    NotificationOverlay.show(
      context,
      message: message,
      title: title,
      backgroundColor: Colors.green[600]!,
      icon: Icons.check_circle,
    );
  }

  static void showErrorNotification(BuildContext context, String message, {String? title}) {
    NotificationOverlay.show(
      context,
      message: message,
      title: title,
      backgroundColor: Colors.red[600]!,
      icon: Icons.error,
      duration: const Duration(seconds: 4),
    );
  }

  static void showInfoNotification(BuildContext context, String message, {String? title}) {
    NotificationOverlay.show(
      context,
      message: message,
      title: title,
      backgroundColor: Colors.blue[600]!,
      icon: Icons.info,
    );
  }

  static void showWarningNotification(BuildContext context, String message, {String? title}) {
    NotificationOverlay.show(
      context,
      message: message,
      title: title,
      backgroundColor: Colors.orange[600]!,
      icon: Icons.warning,
      duration: const Duration(seconds: 4),
    );
  }
} 