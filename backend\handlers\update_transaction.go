package handlers

import (
	"database/sql"
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
)

func UpdateTransactionHandler(c *gin.Context) {
	userID := c.GetInt("userID")
	id := c.Param("id")
	var req struct {
		Amount      float64 `json:"amount"`
		Category    string  `json:"category"`
		Description string  `json:"description"`
		Date        string  `json:"date"`
		Type        string  `json:"type"`
		Notes       *string `json:"notes"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request"})
		return
	}
	db := c.MustGet("db").(*sql.DB)
	res, err := db.Exec("UPDATE transactions SET amount = $1, category = $2, description = $3, date = $4, type = $5, notes = $6 WHERE id = $7 AND user_id = $8", req.Amount, req.Category, req.Description, req.Date, req.Type, req.Notes, id, userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update transaction"})
		return
	}
	n, _ := res.RowsAffected()
	if n == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Transaction not found"})
		return
	}

	// Return the updated transaction
	var updatedTransaction struct {
		ID          int
		Amount      float64
		Category    string
		Description string
		Date        string
		Type        string
		Notes       *string
		IsRecurring bool
		RecurringID *int
	}

	err = db.QueryRow("SELECT id, amount, category, description, date, type, notes, is_recurring, recurring_id FROM transactions WHERE id = $1 AND user_id = $2", id, userID).Scan(&updatedTransaction.ID, &updatedTransaction.Amount, &updatedTransaction.Category, &updatedTransaction.Description, &updatedTransaction.Date, &updatedTransaction.Type, &updatedTransaction.Notes, &updatedTransaction.IsRecurring, &updatedTransaction.RecurringID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve updated transaction"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"id":          fmt.Sprintf("%d", updatedTransaction.ID),
		"amount":      updatedTransaction.Amount,
		"category":    updatedTransaction.Category,
		"description": updatedTransaction.Description,
		"date":        updatedTransaction.Date,
		"type":        updatedTransaction.Type,
		"notes":       updatedTransaction.Notes,
		"isRecurring": updatedTransaction.IsRecurring,
		"recurringId": updatedTransaction.RecurringID,
	})
}
