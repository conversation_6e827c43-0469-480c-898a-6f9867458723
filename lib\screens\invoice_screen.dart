import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/invoice_provider.dart';
import '../models/invoice.dart';
import '../utils/theme.dart';
import '../providers/auth_provider.dart';

class InvoiceScreen extends StatefulWidget {
  final VoidCallback? onStateReady;
  const InvoiceScreen({super.key, this.onStateReady});

  @override
  InvoiceScreenState createState() => InvoiceScreenState();
}

class InvoiceScreenState extends State<InvoiceScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final List<Tab> _tabs = const [
    Tab(text: 'All'),
    Tab(text: 'Unpaid'),
    Tab(text: 'Paid'),
    Tab(text: 'Drafts'),
    Tab(text: 'Overdue'),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabs.length, vsync: this);
    _tabController.addListener(() {
      if (mounted) setState(() {});
    });
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.onStateReady != null) widget.onStateReady!();
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      Provider.of<InvoiceProvider>(context, listen: false).loadInvoices(authProvider: authProvider);
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  List<Invoice> _filterInvoices(List<Invoice> invoices, int tabIndex) {
    switch (tabIndex) {
      case 1:
        // Unpaid
        return invoices.where((i) => i.status == 'unpaid').toList();
      case 2:
        // Paid
        return invoices.where((i) => i.status == 'paid').toList();
      case 3:
        // Drafts
        return invoices.where((i) => i.status == 'draft').toList();
      case 4:
        // Overdue
        return invoices.where((i) => i.status == 'overdue').toList();
      case 0:
      default:
        return invoices;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<InvoiceProvider>(
      builder: (context, invoiceProvider, child) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (invoiceProvider.error != null && invoiceProvider.error!.isNotEmpty) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(invoiceProvider.error!),
                backgroundColor: Colors.red,
              ),
            );
            invoiceProvider.clearError();
          }
        });
        if (invoiceProvider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }
        return Scaffold(
          body: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSummaryCards(invoiceProvider),
                const SizedBox(height: 16),
                TabBar(
                  controller: _tabController,
                  tabs: _tabs,
                  labelColor: Theme.of(context).colorScheme.primary,
                  unselectedLabelColor: Theme.of(context).textTheme.bodyMedium?.color,
                ),
                const SizedBox(height: 16),
                _buildInvoicesListWithData(
                  _filterInvoices(invoiceProvider.invoices, _tabController.index),
                  invoiceProvider,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSummaryCards(InvoiceProvider provider) {
    // Get the most common currency from invoices, default to USD
    String mostCommonCurrency = 'USD';
    if (provider.invoices.isNotEmpty) {
      final currencyCounts = <String, int>{};
      for (final invoice in provider.invoices) {
        currencyCounts[invoice.currency] = (currencyCounts[invoice.currency] ?? 0) + 1;
      }
      mostCommonCurrency = currencyCounts.entries
          .reduce((a, b) => a.value > b.value ? a : b)
          .key;
    }
    
    final symbol = getCurrencySymbol(mostCommonCurrency);
    
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 4, // Four cards in a single row
      crossAxisSpacing: 8,
      mainAxisSpacing: 8,
      childAspectRatio: 0.85, // Make cards more square/compact
      children: [
        _buildSummaryCard(
          'Total Invoiced',
          '$symbol${provider.totalInvoiced.toStringAsFixed(2)}',
          AppTheme.primaryColor,
          Icons.receipt_long,
        ),
        _buildSummaryCard(
          'Total Paid',
          '$symbol${provider.totalPaid.toStringAsFixed(2)}',
          Colors.green,
          Icons.check_circle,
        ),
        _buildSummaryCard(
          'Outstanding',
          '$symbol${provider.totalOutstanding.toStringAsFixed(2)}',
          Colors.orange,
          Icons.pending,
        ),
        _buildSummaryCard(
          'Invoices',
          provider.invoices.length.toString(), // No dollar sign
          Colors.blue,
          Icons.description,
        ),
      ],
    );
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return SizedBox(
      height: 36, // Half the previous height
      child: Card(
        elevation: 4,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 4),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Icon(icon, color: color, size: 28),
              const SizedBox(height: 2),
              Text(
                value,
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: color,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              Text(
                title,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                  fontSize: 12,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInvoicesListWithData(List<Invoice> invoices, InvoiceProvider provider) {
    // Use a Consumer to ensure the list rebuilds when status changes
    return Consumer<InvoiceProvider>(
      builder: (context, invoiceProvider, child) {
        if (invoices.isEmpty) {
          return const Center(child: Text('No invoices found.'));
        }
        return Column(
          children: invoices.map((invoice) => _buildInvoiceItem(invoice, provider)).toList(),
        );
      },
    );
  }

  Widget _buildInvoiceItem(Invoice invoice, InvoiceProvider provider) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getStatusColor(invoice.status),
          child: Icon(
            _getStatusIcon(invoice.status),
            color: Colors.white,
          ),
        ),
        title: Text('Invoice #${invoice.invoiceNumber}'),
        subtitle: Text('${invoice.client.name} • ${_formatDate(invoice.date)}'),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '${getCurrencySymbol(invoice.currency)}${invoice.total.toStringAsFixed(2)}',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(
                  invoice.status.toUpperCase(),
                  style: TextStyle(
                    fontSize: 12,
                    color: _getStatusColor(invoice.status),
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            IconButton(
              icon: const Icon(Icons.delete, color: Colors.red),
              tooltip: 'Delete Invoice',
              onPressed: () async {
                final confirmed = await showDialog<bool>(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: const Text('Delete Invoice'),
                    content: const Text('Are you sure you want to delete this invoice?'),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(false),
                        child: const Text('Cancel'),
                      ),
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(true),
                        child: const Text('Delete', style: TextStyle(color: Colors.red)),
                      ),
                    ],
                  ),
                );
                if (confirmed == true) {
                  final authProvider = Provider.of<AuthProvider>(context, listen: false);
                  await provider.deleteInvoiceById(invoice.id, authProvider: authProvider);
                  await provider.loadInvoices(authProvider: authProvider);
                }
              },
            ),
          ],
        ),
        onTap: () {
          // If draft, open directly in CreateInvoiceDialog for editing
          if (invoice.status == 'draft') {
            showDialog(
              context: context,
              builder: (context) => CreateInvoiceDialog(invoiceToEdit: invoice),
            );
          } else {
            _showInvoiceDetails(context, invoice, provider);
          }
        },
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'draft':
        return Colors.grey;
      case 'unpaid':
        return Colors.blue;
      case 'paid':
        return Colors.green;
      case 'overdue':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'draft':
        return Icons.edit;
      case 'sent':
        return Icons.send;
      case 'paid':
        return Icons.check;
      case 'overdue':
        return Icons.warning;
      default:
        return Icons.description;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.month}/${date.day}/${date.year}';
  }

  // Change _showCreateInvoiceDialog to public
  void showCreateInvoiceDialog(BuildContext context) async {
    final invoiceProvider = Provider.of<InvoiceProvider>(context, listen: false);
    // Create a local in-memory draft invoice
    final localDraft = invoiceProvider.createNewInvoice();

    // Show the dialog immediately with the local draft
    await showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => CreateInvoiceDialog(
        invoiceToEdit: localDraft,
        // No need for onCancel to delete backend draft
      ),
    );
  }

  void _showTemplatesDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const TemplatesDialog(),
    );
  }

  void _showInvoiceDetails(BuildContext context, Invoice invoice, InvoiceProvider provider) {
    showDialog(
      context: context,
      builder: (context) => InvoiceDetailsDialog(invoice: invoice, provider: provider),
    );
  }

  List<Widget> buildAppBarActions(BuildContext context) {
    return [
      ElevatedButton.icon(
        icon: const Icon(Icons.add),
        label: const Text('New Invoice'),
        onPressed: () {
          showCreateInvoiceDialog(context);
        },
      ),
      IconButton(
        icon: const Icon(Icons.article),
        tooltip: 'Templates',
        onPressed: () {
          _showTemplatesDialog(context);
        },
      ),
    ];
  }
}

class CreateInvoiceDialog extends StatefulWidget {
  final Invoice? invoiceToEdit;
  final Future<void> Function()? onCancel;
  final Function(String)? onFinalize;
  const CreateInvoiceDialog({super.key, this.invoiceToEdit, this.onCancel, this.onFinalize});

  @override
  State<CreateInvoiceDialog> createState() => _CreateInvoiceDialogState();
}

class _CreateInvoiceDialogState extends State<CreateInvoiceDialog> {
  final _formKey = GlobalKey<FormState>();
  final _invoiceNumberController = TextEditingController();
  final _clientNameController = TextEditingController();
  final _clientEmailController = TextEditingController();
  final _clientAddressController = TextEditingController();
  List<InvoiceItem> _items = [];
  double get _total => _items.fold(0.0, (sum, item) => sum + (item.price * item.quantity));
  DateTime _selectedDate = DateTime.now();
  DateTime _selectedDueDate = DateTime.now().add(const Duration(days: 30));
  double _taxRate = 0.0;
  String _selectedCurrency = 'USD';
  String? _notes;
  String? _itemError;

  // Track if draft is persisted and backend invoice info
  bool _draftPersisted = false;
  String? _backendInvoiceId;
  String? _backendInvoiceNumber;
  bool _isPersisting = false;

  // New: Track template loading
  bool _templatesLoading = false;

  static const List<String> _currencies = [
    'USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD', 'INR', 'CNY', 'BRL', 'ZAR'
  ];

  @override
  void initState() {
    super.initState();
    if (widget.invoiceToEdit != null) {
      final invoice = widget.invoiceToEdit!;
      _invoiceNumberController.text = invoice.invoiceNumber.isNotEmpty ? invoice.invoiceNumber : 'Generating…';
      _clientNameController.text = invoice.client.name;
      _clientEmailController.text = invoice.client.email;
      _clientAddressController.text = invoice.client.address ?? '';
      _items = List.from(invoice.items);
      _selectedDate = invoice.date;
      _selectedDueDate = invoice.dueDate;
      _taxRate = invoice.taxRate;
      _selectedCurrency = invoice.currency;
      _notes = invoice.notes ?? '';
      if (invoice.id.isNotEmpty && invoice.invoiceNumber.isNotEmpty) {
        _draftPersisted = true;
        _backendInvoiceId = invoice.id;
        _backendInvoiceNumber = invoice.invoiceNumber;
      }
    }
    // Prefetch templates in parallel if not already loaded
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final invoiceProvider = Provider.of<InvoiceProvider>(context, listen: false);
      if (invoiceProvider.templates.isEmpty) {
        setState(() { _templatesLoading = true; });
        final authProvider = Provider.of<AuthProvider>(context, listen: false);
        await invoiceProvider.loadInvoiceTemplates(authProvider: authProvider);
        setState(() { _templatesLoading = false; });
      }
    });
  }

  @override
  void dispose() {
    _invoiceNumberController.dispose();
    _clientNameController.dispose();
    _clientEmailController.dispose();
    _clientAddressController.dispose();
    super.dispose();
  }

  Future<void> _saveToBackend({required String status}) async {
    setState(() { _isPersisting = true; });
    final invoiceProvider = Provider.of<InvoiceProvider>(context, listen: false);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    int attempt = 0;
    int maxAttempts = 3;
    int delayMs = 1000;
    Exception? lastError;
    while (attempt < maxAttempts) {
      try {
        final client = Client(
          name: _clientNameController.text,
          email: _clientEmailController.text,
          address: _clientAddressController.text.isEmpty ? null : _clientAddressController.text,
        );
        Invoice invoice = (widget.invoiceToEdit ?? invoiceProvider.createNewInvoice()).copyWith(
          client: client,
          taxRate: _taxRate,
          currency: _selectedCurrency,
          items: _items,
          notes: _notes,
          status: status,
          date: _selectedDate,
          dueDate: _selectedDueDate,
        );
        // Always update if invoice has an ID (persisted), otherwise create
        if (invoice.id.isNotEmpty) {
          await invoiceProvider.updateInvoice(invoice, authProvider: authProvider, context: context);
        } else {
          await invoiceProvider.createInvoice(invoice, authProvider: authProvider, context: context);
        }
        setState(() {
          _draftPersisted = true;
          _invoiceNumberController.text = 'Generating…';
        });
        await invoiceProvider.loadInvoices(authProvider: authProvider);
        Navigator.of(context).pop();
        setState(() { _isPersisting = false; });
        return;
      } catch (e) {
        lastError = e is Exception ? e : Exception(e.toString());
        attempt++;
        if (attempt < maxAttempts) {
          await Future.delayed(Duration(milliseconds: delayMs));
          delayMs *= 2;
        }
      }
    }
    // If all retries failed, check if offline and allow local save
    setState(() { _isPersisting = false; });
    final isOffline = !(await invoiceProvider.isOnline());
    if (isOffline) {
      // Save draft locally in memory (TODO: persist to local storage)
      // For now, just keep dialog open and show message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Offline: Draft not saved to server. Your data is safe locally. (TODO: persistent local storage)'), backgroundColor: Colors.orange),
      );
      // TODO: Add to a local unsynced drafts list and prompt user to sync when online
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to save invoice after multiple attempts: $lastError'), backgroundColor: Colors.red),
      );
    }
    // User can retry without losing input
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        // No backend draft to delete on cancel
        return true;
      },
      child: AlertDialog(
        title: const Text('Create Invoice'),
        content: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 800, minWidth: 500),
          child: SingleChildScrollView(
            child: Form(
              key: _formKey,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextFormField(
                    controller: _invoiceNumberController,
                    decoration: const InputDecoration(labelText: 'Invoice Number'),
                    readOnly: true,
                    enableInteractiveSelection: false,
                    style: const TextStyle(),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter invoice number';
                      }
                      return null;
                    },
                    focusNode: AlwaysDisabledFocusNode(),
                  ),
                  const SizedBox(height: 16),
                  DropdownButtonFormField<String>(
                    value: _selectedCurrency,
                    decoration: const InputDecoration(labelText: 'Currency'),
                    items: _currencies.map((c) => DropdownMenuItem(value: c, child: Text(c))).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedCurrency = value!;
                      });
                    },
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: _clientNameController,
                    decoration: const InputDecoration(labelText: 'Client Name'),
                    validator: (value) => value == null || value.isEmpty ? 'Client name is required' : null,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: _clientEmailController,
                    decoration: const InputDecoration(labelText: 'Client Email'),
                    validator: (value) => value == null || value.isEmpty ? 'Client email is required' : null,
                  ),
                  // Items section
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          const Text('Items', style: TextStyle(fontWeight: FontWeight.bold)),
                          if (_itemError != null && _itemError!.isNotEmpty)
                            Padding(
                              padding: const EdgeInsets.only(left: 12.0),
                              child: Text(
                                _itemError!,
                                style: const TextStyle(color: Colors.red, fontWeight: FontWeight.bold),
                              ),
                            ),
                        ],
                      ),
                      TextButton(
                        onPressed: () {
                          setState(() {
                            _itemError = null;
                          });
                          _addItem();
                        },
                        child: const Text('Add Item'),
                      ),
                    ],
                  ),
                  ..._items.asMap().entries.map((entry) {
                    final index = entry.key;
                    final item = entry.value;
                    return Card(
                      margin: const EdgeInsets.symmetric(vertical: 4),
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Row(
                          children: [
                            Expanded(
                              flex: 3,
                              child: TextFormField(
                                initialValue: item.description,
                                decoration: const InputDecoration(labelText: 'Description'),
                                onChanged: (val) => _updateItem(index, item.copyWith(description: val)),
                                validator: (val) => val == null || val.isEmpty ? 'Required' : null,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              flex: 2,
                              child: TextFormField(
                                initialValue: item.quantity.toString(),
                                decoration: const InputDecoration(labelText: 'Qty'),
                                keyboardType: TextInputType.number,
                                onChanged: (val) {
                                  final qty = int.tryParse(val) ?? 1;
                                  _updateItem(index, item.copyWith(quantity: qty));
                                },
                                validator: (val) {
                                  final qty = int.tryParse(val ?? '');
                                  if (qty == null || qty <= 0) return 'Invalid';
                                  return null;
                                },
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              flex: 3,
                              child: TextFormField(
                                initialValue: item.price.toStringAsFixed(2),
                                decoration: const InputDecoration(labelText: 'Price'),
                                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                                onChanged: (val) {
                                  final price = double.tryParse(val) ?? 0.0;
                                  _updateItem(index, item.copyWith(price: price));
                                },
                                validator: (val) {
                                  final price = double.tryParse(val ?? '');
                                  if (price == null || price < 0) return 'Invalid';
                                  return null;
                                },
                              ),
                            ),
                            IconButton(
                              icon: const Icon(Icons.delete, color: Colors.red),
                              onPressed: () => _removeItem(index),
                            ),
                          ],
                        ),
                      ),
                    );
                  }),
                  const SizedBox(height: 16),
                  // Readonly total field
                  TextFormField(
                    readOnly: true,
                    enabled: true,
                    enableInteractiveSelection: false,
                    style: const TextStyle(), // keep style same as others
                    decoration: InputDecoration(
                      labelText: 'Total',
                      suffixText: _selectedCurrency,
                    ),
                    controller: TextEditingController(text: _total.toStringAsFixed(2)),
                    focusNode: AlwaysDisabledFocusNode(),
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    decoration: const InputDecoration(labelText: 'Tax Rate (%)'),
                    keyboardType: TextInputType.number,
                    initialValue: _taxRate.toString(),
                    onChanged: (value) {
                      _taxRate = double.tryParse(value) ?? 0.0;
                    },
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: TextEditingController(text: _notes),
                    decoration: const InputDecoration(labelText: 'Notes (Optional)'),
                    maxLines: 3,
                    onChanged: (value) => _notes = value,
                  ),
                ],
              ),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: _isPersisting ? null : () async {
              Navigator.of(context).pop();
            },
            child: const Text('Cancel'),
          ),
          // Discard Draft button (only for drafts)
          if ((widget.invoiceToEdit?.status ?? 'draft') == 'draft' && _draftPersisted && _backendInvoiceId != null)
            TextButton(
              onPressed: _isPersisting ? null : () async {
                final confirmed = await showDialog<bool>(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: const Text('Discard Draft'),
                    content: const Text('Are you sure you want to discard this draft? This action cannot be undone.'),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(false),
                        child: const Text('Cancel'),
                      ),
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(true),
                        child: const Text('Discard'),
                      ),
                    ],
                  ),
                );
                if (confirmed == true) {
                  final invoiceProvider = Provider.of<InvoiceProvider>(context, listen: false);
                  final authProvider = Provider.of<AuthProvider>(context, listen: false);
                  await invoiceProvider.deleteInvoiceById(_backendInvoiceId!, authProvider: authProvider);
                  await invoiceProvider.loadInvoices(authProvider: authProvider);
                  Navigator.of(context).pop();
                }
              },
              child: const Text('Discard Draft', style: TextStyle(color: Colors.red)),
            ),
          // Save as Draft button
          ElevatedButton(
            onPressed: _isPersisting ? null : () async {
              if (_formKey.currentState!.validate()) {
                await _saveToBackend(status: 'draft');
              }
            },
            child: const Text('Save as Draft'),
          ),
          ElevatedButton(
            onPressed: _isPersisting ? null : () async {
              setState(() {
                _itemError = null;
              });
              if (_formKey.currentState!.validate()) {
                if (_items.isEmpty) {
                  setState(() {
                    _itemError = 'Add at least one item to create the invoice';
                  });
                  return;
                }
                // Validate each item
                for (int i = 0; i < _items.length; i++) {
                  final item = _items[i];
                  if (item.description.trim().isEmpty) {
                    setState(() {
                      _itemError = 'Item  ${i + 1}: Description is required.';
                    });
                    return;
                  }
                  if (item.quantity <= 0) {
                    setState(() {
                      _itemError = 'Item ${i + 1}: Quantity must be greater than 0.';
                    });
                    return;
                  }
                  if (item.price < 0) {
                    setState(() {
                      _itemError = 'Item ${i + 1}: Price must be 0 or greater.';
                    });
                    return;
                  }
                }
                await _saveToBackend(status: 'unpaid');
              }
            },
            child: const Text('Create'),
          ),
          if (_isPersisting)
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.0),
              child: SizedBox(width: 24, height: 24, child: CircularProgressIndicator(strokeWidth: 2)),
            ),
        ],
      ),
    );
  }

  void _addItem() {
    setState(() {
      _items.add(InvoiceItem(description: '', quantity: 1, price: 0.0));
    });
  }

  void _removeItem(int index) {
    setState(() {
      _items.removeAt(index);
    });
  }

  void _updateItem(int index, InvoiceItem item) {
    setState(() {
      _items[index] = item;
    });
  }
}

class TemplatesDialog extends StatelessWidget {
  const TemplatesDialog({super.key});

  @override
  Widget build(BuildContext context) {
    final invoiceProvider = Provider.of<InvoiceProvider>(context);
    final templatesLoading = (context.findAncestorStateOfType<_CreateInvoiceDialogState>()?._templatesLoading) ?? false;
    return AlertDialog(
      title: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          const Text('Invoice Templates'),
          IconButton(
            icon: const Icon(Icons.add),
            tooltip: 'New Template',
            onPressed: () => _showEditTemplateDialog(context, invoiceProvider),
          ),
        ],
      ),
      content: SizedBox(
        width: 400,
        child: templatesLoading
            ? const Center(child: CircularProgressIndicator())
            : invoiceProvider.templates.isEmpty
                ? const Text('No templates found.')
                : ListView.builder(
                    shrinkWrap: true,
                    itemCount: invoiceProvider.templates.length,
                    itemBuilder: (context, index) {
                      final template = invoiceProvider.templates[index];
                      return Card(
                        child: ListTile(
                          title: Text(template.name),
                          subtitle: Text('Tax: ${template.defaultTaxRate}%'),
                          trailing: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              IconButton(
                                icon: const Icon(Icons.edit),
                                tooltip: 'Edit',
                                onPressed: () => _showEditTemplateDialog(context, invoiceProvider, template: template),
                              ),
                              IconButton(
                                icon: const Icon(Icons.delete),
                                tooltip: 'Delete',
                                onPressed: () async {
                                  final authProvider = Provider.of<AuthProvider>(context, listen: false);
                                  await invoiceProvider.deleteTemplate(template.id.toString(), authProvider: authProvider);
                                },
                              ),
                              IconButton(
                                icon: const Icon(Icons.add_circle_outline),
                                tooltip: 'Use Template',
                                onPressed: templatesLoading ? null : () {
                                  final newInvoice = invoiceProvider.createFromTemplate(template);
                                  Navigator.of(context).pop();
                                  showDialog(
                                    context: context,
                                    builder: (context) => CreateInvoiceDialog(invoiceToEdit: newInvoice),
                                  );
                                },
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
      ],
    );
  }

  void _showEditTemplateDialog(BuildContext context, InvoiceProvider provider, {InvoiceTemplate? template}) {
    showDialog(
      context: context,
      builder: (context) => EditTemplateDialog(provider: provider, template: template),
    );
  }
}

class EditTemplateDialog extends StatefulWidget {
  final InvoiceProvider provider;
  final InvoiceTemplate? template;
  const EditTemplateDialog({super.key, required this.provider, this.template});

  @override
  State<EditTemplateDialog> createState() => _EditTemplateDialogState();
}

class _EditTemplateDialogState extends State<EditTemplateDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _taxController;
  late TextEditingController _notesController;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.template?.name ?? '');
    _taxController = TextEditingController(text: widget.template?.defaultTaxRate.toString() ?? '0');
    _notesController = TextEditingController(text: widget.template?.notes ?? '');
  }

  @override
  void dispose() {
    _nameController.dispose();
    _taxController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.template == null ? 'New Template' : 'Edit Template'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(labelText: 'Template Name'),
                validator: (value) => value == null || value.isEmpty ? 'Enter a name' : null,
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _taxController,
                decoration: const InputDecoration(labelText: 'Default Tax Rate (%)'),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _notesController,
                decoration: const InputDecoration(labelText: 'Notes (Optional)'),
                maxLines: 3,
              ),
              // TODO: Add default items editing UI
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _saveTemplate,
          child: const Text('Save'),
        ),
      ],
    );
  }

  void _saveTemplate() async {
    if (!_formKey.currentState!.validate()) return;
    final name = _nameController.text;
    final tax = double.tryParse(_taxController.text) ?? 0.0;
    final notes = _notesController.text.isEmpty ? null : _notesController.text;
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    if (widget.template == null) {
      await widget.provider.createTemplate(
        name: name,
        defaultTaxRate: tax,
        notes: notes,
        authProvider: authProvider,
      );
    } else {
      await widget.provider.updateTemplate(
        id: widget.template!.id.toString(),
        name: name,
        defaultTaxRate: tax,
        notes: notes,
        authProvider: authProvider,
      );
    }
    Navigator.of(context).pop();
  }
}

class InvoiceDetailsDialog extends StatelessWidget {
  final Invoice invoice;
  final InvoiceProvider provider;
  const InvoiceDetailsDialog({super.key, required this.invoice, required this.provider});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 600),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text('Invoice #${invoice.invoiceNumber}', style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold)),
                const SizedBox(height: 16),
                Card(
                  elevation: 2,
                  margin: const EdgeInsets.only(bottom: 16),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('Client: ${invoice.client.name}', style: const TextStyle(fontWeight: FontWeight.bold)),
                        Text('Email: ${invoice.client.email}'),
                        if (invoice.client.address != null) Text('Address: ${invoice.client.address}'),
                        if (invoice.client.phone != null) Text('Phone: ${invoice.client.phone}'),
                      ],
                    ),
                  ),
                ),
                Card(
                  elevation: 2,
                  margin: const EdgeInsets.only(bottom: 16),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('Date: ${_formatDate(invoice.date)}'),
                        Text('Due: ${_formatDate(invoice.dueDate)}'),
                        Text('Status: ${invoice.status.toUpperCase()}', style: TextStyle(fontWeight: FontWeight.bold, color: _getStatusColor(invoice.status))),
                      ],
                    ),
                  ),
                ),
                Card(
                  elevation: 2,
                  margin: const EdgeInsets.only(bottom: 16),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('Items:', style: Theme.of(context).textTheme.titleMedium),
                        ...invoice.items.map((item) => Padding(
                          padding: const EdgeInsets.symmetric(vertical: 2),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(child: Text(item.description)),
                              Text('x${item.quantity}'),
                              Text('${getCurrencySymbol(invoice.currency)}${item.price.toStringAsFixed(2)}'),
                              Text('= ${getCurrencySymbol(invoice.currency)}${(item.total).toStringAsFixed(2)}'),
                            ],
                          ),
                        )),
                        const Divider(),
                        Text('Subtotal:   ${getCurrencySymbol(invoice.currency)}${invoice.subtotal.toStringAsFixed(2)}'),
                        Text('Tax (${invoice.taxRate}%):   ${getCurrencySymbol(invoice.currency)}${invoice.taxAmount.toStringAsFixed(2)}'),
                        Text('Total:   ${getCurrencySymbol(invoice.currency)}${invoice.total.toStringAsFixed(2)}', style: const TextStyle(fontWeight: FontWeight.bold)),
                        if (invoice.notes != null && invoice.notes!.isNotEmpty) ...[
                          const SizedBox(height: 8),
                          Text('Notes: ${invoice.notes}'),
                        ],
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                Wrap(
                  alignment: WrapAlignment.center,
                  spacing: 16,
                  runSpacing: 8,
                  children: [
                    ElevatedButton.icon(
                      icon: const Icon(Icons.edit),
                      label: const Text('Edit'),
                      onPressed: () async {
                        Navigator.of(context).pop();
                        await showDialog(
                          context: context,
                          builder: (context) => CreateInvoiceDialog(
                            invoiceToEdit: invoice,
                          ),
                        );
                      },
                    ),
                    ElevatedButton.icon(
                      icon: const Icon(Icons.delete),
                      label: const Text('Delete'),
                      style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                      onPressed: () async {
                        final authProvider = Provider.of<AuthProvider>(context, listen: false);
                        await provider.deleteInvoice(invoice.id, authProvider: authProvider, context: context);
                        Navigator.of(context).pop();
                      },
                    ),
                    ElevatedButton.icon(
                      icon: const Icon(Icons.send),
                      label: const Text('Send'),
                      onPressed: () async {
                        final authProvider = Provider.of<AuthProvider>(context, listen: false);
                        await provider.sendInvoice(invoice, authProvider: authProvider, context: context);
                        await provider.loadInvoices(authProvider: authProvider);
                        Navigator.of(context).pop();
                      },
                    ),
                    ElevatedButton.icon(
                      icon: const Icon(Icons.check_circle),
                      label: const Text('Mark Paid'),
                      onPressed: () async {
                        final authProvider = Provider.of<AuthProvider>(context, listen: false);
                        final paidInvoice = invoice.copyWith(status: 'paid');
                        await provider.updateInvoice(paidInvoice, authProvider: authProvider, context: context);
                        await provider.loadInvoices(authProvider: authProvider);
                        Navigator.of(context).pop();
                      },
                    ),
                    ElevatedButton.icon(
                      icon: const Icon(Icons.picture_as_pdf),
                      label: const Text('PDF'),
                      onPressed: () async {
                        final authProvider = Provider.of<AuthProvider>(context, listen: false);
                        await provider.generatePDF(invoice, authProvider: authProvider, context: context);
                      },
                    ),
                    TextButton(
                      child: const Text('Close'),
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.month}/${date.day}/${date.year}';
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'draft':
        return Colors.grey;
      case 'sent':
        return Colors.red;
      case 'paid':
        return Colors.green;
      case 'overdue':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}

class EditInvoiceDialog extends StatefulWidget {
  final Invoice invoice;
  const EditInvoiceDialog({super.key, required this.invoice});

  @override
  State<EditInvoiceDialog> createState() => _EditInvoiceDialogState();
}

class _EditInvoiceDialogState extends State<EditInvoiceDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _invoiceNumberController;
  late TextEditingController _clientNameController;
  late TextEditingController _clientEmailController;
  late TextEditingController _clientAddressController;
  late List<InvoiceItem> _items;
  late DateTime _selectedDate;
  late DateTime _selectedDueDate;
  late double _taxRate;
  late String _selectedCurrency;
  String? _notes;

  double get _total => _items.fold(0.0, (sum, item) => sum + (item.price * item.quantity));

  @override
  void initState() {
    super.initState();
    final invoice = widget.invoice;
    _invoiceNumberController = TextEditingController(text: invoice.invoiceNumber);
    _clientNameController = TextEditingController(text: invoice.client.name);
    _clientEmailController = TextEditingController(text: invoice.client.email);
    _clientAddressController = TextEditingController(text: invoice.client.address ?? '');
    _items = List.from(invoice.items);
    _selectedDate = invoice.date;
    _selectedDueDate = invoice.dueDate;
    _taxRate = invoice.taxRate;
    _selectedCurrency = invoice.currency;
    _notes = invoice.notes ?? '';
  }

  void _addItem() {
    setState(() {
      _items.add(InvoiceItem(description: '', quantity: 1, price: 0.0));
    });
  }

  void _removeItem(int index) {
    setState(() {
      _items.removeAt(index);
    });
  }

  void _updateItem(int index, InvoiceItem item) {
    setState(() {
      _items[index] = item;
    });
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        // Just close, do not save
        return true;
      },
      child: AlertDialog(
        title: const Text('Edit Invoice'),
        content: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 800, minWidth: 500),
          child: SingleChildScrollView(
            child: Form(
              key: _formKey,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextFormField(
                    controller: _invoiceNumberController,
                    decoration: const InputDecoration(labelText: 'Invoice Number'),
                    readOnly: true,
                    enableInteractiveSelection: false,
                    style: const TextStyle(), // keep style same as others
                    validator: (value) => value == null || value.isEmpty ? 'Invoice number is required' : null,
                    focusNode: AlwaysDisabledFocusNode(),
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: _clientNameController,
                    decoration: const InputDecoration(labelText: 'Client Name'),
                    validator: (value) => value == null || value.isEmpty ? 'Client name is required' : null,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: _clientEmailController,
                    decoration: const InputDecoration(labelText: 'Client Email'),
                    validator: (value) => value == null || value.isEmpty ? 'Client email is required' : null,
                  ),
                  const SizedBox(height: 16),
                  // Items section
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('Items', style: TextStyle(fontWeight: FontWeight.bold)),
                      TextButton(
                        onPressed: _addItem,
                        child: const Text('Add Item'),
                      ),
                    ],
                  ),
                  ..._items.asMap().entries.map((entry) {
                    final index = entry.key;
                    final item = entry.value;
                    return Card(
                      margin: const EdgeInsets.symmetric(vertical: 4),
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Row(
                          children: [
                            Expanded(
                              flex: 3,
                              child: TextFormField(
                                initialValue: item.description,
                                decoration: const InputDecoration(labelText: 'Description'),
                                onChanged: (val) => _updateItem(index, item.copyWith(description: val)),
                                validator: (val) => val == null || val.isEmpty ? 'Required' : null,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              flex: 2,
                              child: TextFormField(
                                initialValue: item.quantity.toString(),
                                decoration: const InputDecoration(labelText: 'Qty'),
                                keyboardType: TextInputType.number,
                                onChanged: (val) {
                                  final qty = int.tryParse(val) ?? 1;
                                  _updateItem(index, item.copyWith(quantity: qty));
                                },
                                validator: (val) {
                                  final qty = int.tryParse(val ?? '');
                                  if (qty == null || qty <= 0) return 'Invalid';
                                  return null;
                                },
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              flex: 3,
                              child: TextFormField(
                                initialValue: item.price.toStringAsFixed(2),
                                decoration: const InputDecoration(labelText: 'Price'),
                                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                                onChanged: (val) {
                                  final price = double.tryParse(val) ?? 0.0;
                                  _updateItem(index, item.copyWith(price: price));
                                },
                                validator: (val) {
                                  final price = double.tryParse(val ?? '');
                                  if (price == null || price < 0) return 'Invalid';
                                  return null;
                                },
                              ),
                            ),
                            IconButton(
                              icon: const Icon(Icons.delete, color: Colors.red),
                              onPressed: () => _removeItem(index),
                            ),
                          ],
                        ),
                      ),
                    );
                  }),
                  const SizedBox(height: 16),
                  // Readonly total field
                  TextFormField(
                    readOnly: true,
                    enabled: true,
                    enableInteractiveSelection: false,
                    style: const TextStyle(), // keep style same as others
                    decoration: InputDecoration(
                      labelText: 'Total',
                      suffixText: _selectedCurrency,
                    ),
                    controller: TextEditingController(text: _total.toStringAsFixed(2)),
                    focusNode: AlwaysDisabledFocusNode(),
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: TextEditingController(text: _notes),
                    decoration: const InputDecoration(labelText: 'Notes (Optional)'),
                    maxLines: 3,
                    onChanged: (value) => _notes = value,
                  ),
                ],
              ),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          // Discard Draft button (only for drafts)
          if (widget.invoice.status == 'draft')
            TextButton(
              onPressed: () async {
                final confirmed = await showDialog<bool>(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: const Text('Discard Draft'),
                    content: const Text('Are you sure you want to discard this draft? This action cannot be undone.'),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(false),
                        child: const Text('Cancel'),
                      ),
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(true),
                        child: const Text('Discard'),
                      ),
                    ],
                  ),
                );
                if (confirmed == true) {
                  final invoiceProvider = Provider.of<InvoiceProvider>(context, listen: false);
                  final authProvider = Provider.of<AuthProvider>(context, listen: false);
                  await invoiceProvider.deleteInvoiceById(widget.invoice.id, authProvider: authProvider);
                  await invoiceProvider.loadInvoices(authProvider: authProvider);
                  Navigator.of(context).pop();
                }
              },
              child: const Text('Discard Draft', style: TextStyle(color: Colors.red)),
            ),
          ElevatedButton(
            onPressed: () async {
              if (_formKey.currentState!.validate() && _items.isNotEmpty) {
                final invoiceProvider = Provider.of<InvoiceProvider>(context, listen: false);
                final authProvider = Provider.of<AuthProvider>(context, listen: false);
                final client = Client(
                  name: _clientNameController.text,
                  email: _clientEmailController.text,
                  address: _clientAddressController.text.isEmpty ? null : _clientAddressController.text,
                );
                final updatedInvoice = widget.invoice.copyWith(
                  invoiceNumber: _invoiceNumberController.text,
                  client: client,
                  items: _items,
                  taxRate: _taxRate,
                  currency: _selectedCurrency,
                  notes: _notes,
                  date: _selectedDate,
                  dueDate: _selectedDueDate,
                );
                await invoiceProvider.updateInvoice(updatedInvoice, authProvider: authProvider, context: context);
                Navigator.of(context).pop();
              }
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }
}

class AlwaysDisabledFocusNode extends FocusNode {
  @override
  bool get hasFocus => false;
}

String getCurrencySymbol(String code) {
  switch (code) {
    case 'USD': return '\$';
    case 'EUR': return '€';
    case 'GBP': return '£';
    case 'JPY': return '¥';
    case 'CAD': return 'C\$';
    case 'AUD': return 'A\$';
    case 'INR': return '₹';
    case 'CNY': return '¥';
    case 'BRL': return 'R\$';
    case 'ZAR': return 'R';
    default: return code;
  }
}