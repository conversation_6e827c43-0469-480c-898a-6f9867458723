package handlers

import (
	"database/sql"
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
)

func GetTransactionHandler(c *gin.Context) {
	userID := c.GetInt("userID")
	id := c.<PERSON>m("id")
	var t struct {
		ID          int
		Amount      float64
		Category    string
		Description string
		Date        string
		Type        string
		Notes       *string
		IsRecurring bool
		RecurringID *int
	}
	db := c.Must<PERSON>et("db").(*sql.DB)
	err := db.QueryRow("SELECT id, amount, category, description, date, type, notes, is_recurring, recurring_id FROM transactions WHERE id = $1 AND user_id = $2", id, userID).Scan(&t.ID, &t.Amount, &t.Category, &t.Description, &t.Date, &t.Type, &t.Notes, &t.IsRecurring, &t.RecurringID)
	if err == sql.ErrNoRows {
		c.JSON(http.StatusNotFound, gin.H{"error": "Transaction not found"})
		return
	} else if err != nil {
		c.<PERSON>(http.StatusInternalServerError, gin.H{"error": "Database error"})
		return
	}
	c.<PERSON>(http.StatusOK, gin.H{
		"id":          fmt.Sprintf("%d", t.ID),
		"amount":      t.Amount,
		"category":    t.Category,
		"description": t.Description,
		"date":        t.Date,
		"type":        t.Type,
		"notes":       t.Notes,
		"isRecurring": t.IsRecurring,
		"recurringId": t.RecurringID,
	})
}
