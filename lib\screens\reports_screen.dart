import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fl_chart/fl_chart.dart';
import '../providers/expense_provider.dart';
import '../utils/theme.dart';

class ReportsScreen extends StatelessWidget {
  const ReportsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final expenseProvider = Provider.of<ExpenseProvider>(context);
    return Scaffold(
      // Remove the appBar property
      // appBar: AppBar(title: const Text('Reports & Analytics')),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSummaryCards(expenseProvider),
            const SizedBox(height: 24),
            Text('Spending by Category', style: Theme.of(context).textTheme.headlineSmall),
            const SizedBox(height: 16),
            _buildCategoryPie<PERSON>hart(expenseProvider),
            const SizedBox(height: 32),
            Text('Income vs Expenses (Monthly)', style: Theme.of(context).textTheme.headlineSmall),
            const SizedBox(height: 16),
            _buildIncomeExpenseBarChart(expenseProvider),
            const SizedBox(height: 32),
            Text('Monthly Summary', style: Theme.of(context).textTheme.headlineSmall),
            const SizedBox(height: 16),
            _buildMonthlySummary(expenseProvider),
          ],
        ),
      ),
    );
  }

  // Add this method to expose AppBar actions for HomeScreen
  List<Widget> buildAppBarActions(BuildContext context) => [];

  Widget _buildSummaryCards(ExpenseProvider provider) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 4, // Four cards in a single row
      crossAxisSpacing: 8,
      mainAxisSpacing: 8,
      childAspectRatio: 0.85, // Make cards more square/compact
      children: [
        _buildSummaryCard(
          'Total Balance',
          '\$${provider.totalBalance.toStringAsFixed(2)}',
          provider.totalBalance >= 0 ? Colors.green : Colors.red,
          Icons.account_balance_wallet,
        ),
        _buildSummaryCard(
          'Total Income',
          '\$${provider.totalIncome.toStringAsFixed(2)}',
          Colors.green,
          Icons.trending_up,
        ),
        _buildSummaryCard(
          'Total Expenses',
          '\$${provider.totalExpenses.toStringAsFixed(2)}',
          Colors.red,
          Icons.trending_down,
        ),
        _buildSummaryCard(
          'Transactions',
          provider.transactions.length.toString(),
          AppTheme.primaryColor,
          Icons.receipt,
        ),
      ],
    );
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return SizedBox(
      height: 36, // Half the previous height
      child: Card(
        elevation: 4,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 4),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Icon(icon, color: color, size: 28),
              const SizedBox(height: 2),
              Text(
                value,
                style: TextStyle(
                  color: color,
                  fontWeight: FontWeight.bold,
                  fontSize: 20,
                ),
                textAlign: TextAlign.center,
              ),
              Text(
                title,
                style: const TextStyle(color: Colors.grey, fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCategoryPieChart(ExpenseProvider provider) {
    final categoryTotals = provider.categoryTotals;
    if (categoryTotals.isEmpty) {
      return const Text('No expense data available.');
    }
    final colors = [
      Colors.blue,
      Colors.red,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.teal,
      Colors.pink,
      Colors.indigo,
    ];
    return SizedBox(
      height: 200,
      child: PieChart(
        PieChartData(
          sections: categoryTotals.entries.map((entry) {
            final index = categoryTotals.keys.toList().indexOf(entry.key);
            return PieChartSectionData(
              value: entry.value,
              title: '${entry.key}\n\$${entry.value.toStringAsFixed(0)}',
              color: colors[index % colors.length],
              radius: 60,
              titleStyle: const TextStyle(fontSize: 12, fontWeight: FontWeight.bold, color: Colors.white),
            );
          }).toList(),
          centerSpaceRadius: 40,
        ),
      ),
    );
  }

  Widget _buildIncomeExpenseBarChart(ExpenseProvider provider) {
    final transactions = provider.transactions;
    if (transactions.isEmpty) {
      return const Text('No transaction data available.');
    }
    // Group by month
    final Map<String, double> incomeByMonth = {};
    final Map<String, double> expenseByMonth = {};
    for (var t in transactions) {
      final key = '${t.date.year}-${t.date.month.toString().padLeft(2, '0')}';
      if (t.type == 'income') {
        incomeByMonth[key] = (incomeByMonth[key] ?? 0) + t.amount;
      } else {
        expenseByMonth[key] = (expenseByMonth[key] ?? 0) + t.amount;
      }
    }
    final months = {...incomeByMonth.keys, ...expenseByMonth.keys}.toList()..sort();
    return SizedBox(
      height: 220,
      child: BarChart(
        BarChartData(
          barGroups: List.generate(months.length, (i) {
            final month = months[i];
            return BarChartGroupData(
              x: i,
              barRods: [
                BarChartRodData(
                  toY: incomeByMonth[month] ?? 0,
                  color: Colors.green,
                  width: 8,
                ),
                BarChartRodData(
                  toY: expenseByMonth[month] ?? 0,
                  color: Colors.red,
                  width: 8,
                ),
              ],
            );
          }),
          titlesData: FlTitlesData(
            leftTitles: const AxisTitles(
              sideTitles: SideTitles(showTitles: true, reservedSize: 40),
            ),
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                getTitlesWidget: (value, meta) {
                  final i = value.toInt();
                  if (i < 0 || i >= months.length) return const SizedBox.shrink();
                  final parts = months[i].split('-');
                  return Text('${parts[1]}/${parts[0]}', style: const TextStyle(fontSize: 10));
                },
              ),
            ),
            rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
            topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          ),
          gridData: const FlGridData(show: true),
          borderData: FlBorderData(show: false),
        ),
      ),
    );
  }

  Widget _buildMonthlySummary(ExpenseProvider provider) {
    final now = DateTime.now();
    final thisMonth = provider.transactions.where((t) => t.date.year == now.year && t.date.month == now.month);
    final income = thisMonth.where((t) => t.type == 'income').fold(0.0, (sum, t) => sum + t.amount);
    final expenses = thisMonth.where((t) => t.type == 'expense').fold(0.0, (sum, t) => sum + t.amount);
    final balance = income - expenses;
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Income:   \$${income.toStringAsFixed(2)}', style: const TextStyle(color: Colors.green)),
            Text('Expenses: \$${expenses.toStringAsFixed(2)}', style: const TextStyle(color: Colors.red)),
            const SizedBox(height: 8),
            Text('Balance:  \$${balance.toStringAsFixed(2)}', style: TextStyle(color: balance >= 0 ? Colors.green : Colors.red, fontWeight: FontWeight.bold)),
          ],
        ),
      ),
    );
  }
} 