import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/api/auth.dart';

class AuthProvider with ChangeNotifier {
  bool _isAuthenticated = false;
  String? _userId;
  String? _userEmail;
  String? _token;
  bool _isLoading = false;
  String? _error;
  String? _emailError;
  String? _passwordError;
  final AuthApi apiService;
  AuthProvider({required this.apiService}) {
    apiService.setOnUnauthorized(() {
      signOut();
    });
  }

  bool get isAuthenticated => _isAuthenticated;
  String? get userId => _userId;
  String? get userEmail => _userEmail;
  String? get token => _token;
  bool get isLoading => _isLoading;
  String? get error => _error;
  String? get emailError => _emailError;
  String? get passwordError => _passwordError;

  static const String _baseUrl = 'http://localhost:8080/api/v1';

  Future<void> initialize() async {
    _setLoading(true);
    try {
      final prefs = await SharedPreferences.getInstance();
      _isAuthenticated = prefs.getBool('isAuthenticated') ?? false;
      _userId = prefs.getString('userId');
      _userEmail = prefs.getString('userEmail');
      _token = prefs.getString('token');
      _error = null;
      if (_token != null) {
        apiService.setAuthToken(_token!);
      }
    } catch (e) {
      _error = 'Failed to initialize auth: $e';
    } finally {
      _setLoading(false);
    }
  }

  Future<void> signIn(String email, String password) async {
    _setLoading(true);
    _emailError = null;
    _passwordError = null;
    try {
      final data = await apiService.login(email, password);
      if (data['token'] != null) {
        _isAuthenticated = true;
        _userEmail = email;
        _token = data['token'];
        _userId = data['userId']?.toString();
        apiService.setAuthToken(_token!);
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool('isAuthenticated', true);
        await prefs.setString('userId', _userId ?? '');
        await prefs.setString('userEmail', _userEmail ?? '');
        await prefs.setString('token', _token!);
        _error = null;
        notifyListeners();
      } else {
        _error = data['error'] ?? 'Login failed';
        // Field-specific errors
        if (data['field_errors'] != null) {
          _emailError = data['field_errors']['email'];
          _passwordError = data['field_errors']['password'];
        }
      }
    } catch (e) {
      _error = 'Failed to sign in: $e';
    } finally {
      _setLoading(false);
    }
  }

  Future<void> signUp(String email, String password, String name) async {
    _setLoading(true);
    _emailError = null;
    _passwordError = null;
    try {
      final data = await apiService.register(email, password, name);
      if (data['token'] != null) {
        _isAuthenticated = true;
        _userEmail = email;
        _token = data['token'];
        _userId = data['userId']?.toString();
        apiService.setAuthToken(_token!);
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool('isAuthenticated', true);
        await prefs.setString('userId', _userId ?? '');
        await prefs.setString('userEmail', _userEmail ?? '');
        await prefs.setString('token', _token!);
        _error = null;
        notifyListeners();
      } else {
        _error = data['error'] ?? 'Registration failed';
        // Field-specific errors
        if (data['field_errors'] != null) {
          _emailError = data['field_errors']['email'];
          _passwordError = data['field_errors']['password'];
        }
      }
    } catch (e) {
      _error = 'Failed to sign up: $e';
    } finally {
      _setLoading(false);
    }
  }

  Future<void> signOut() async {
    _setLoading(true);
    try {
      _isAuthenticated = false;
      _userId = null;
      _userEmail = null;
      _token = null;
      apiService.setAuthToken('');
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();
      _error = null;
      notifyListeners();
    } catch (e) {
      _error = 'Failed to sign out: $e';
    } finally {
      _setLoading(false);
    }
  }

  Future<void> resetPassword(String email) async {
    _setLoading(true);
    try {
      await apiService.resetPassword(email);
      _error = null;
    } catch (e) {
      _error = 'Failed to reset password: $e';
    } finally {
      _setLoading(false);
    }
  }

  Future<void> confirmPasswordReset(String token, String newPassword) async {
    _setLoading(true);
    try {
      await apiService.confirmPasswordReset(token, newPassword);
      _error = null;
    } catch (e) {
      _error = 'Failed to reset password: $e';
    } finally {
      _setLoading(false);
    }
  }

  Future<String?> deleteAccount(String password) async {
    _setLoading(true);
    try {
      await apiService.deleteAccount(password);
      await signOut();
      return null;
    } catch (e) {
      return e.toString();
    } finally {
      _setLoading(false);
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void clearError() {
    _error = null;
    _emailError = null;
    _passwordError = null;
    notifyListeners();
  }

  static Future<String?> getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('token');
  }
} 