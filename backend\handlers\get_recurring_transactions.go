package handlers

import (
	"database/sql"
	"net/http"

	"github.com/gin-gonic/gin"
)

func GetRecurringTransactionsHandler(c *gin.Context) {
	userID := c.GetInt("userID")
	db := c.MustGet("db").(*sql.DB)
	rows, err := db.Query("SELECT id, description, amount, category, type, frequency, start_date, end_date, notes, created_at FROM recurring_transactions WHERE user_id = $1 ORDER BY start_date DESC", userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error"})
		return
	}
	defer rows.Close()
	recurring := []gin.H{}
	for rows.Next() {
		var r struct {
			ID          int
			Description string
			Amount      float64
			Category    string
			Type        string
			Frequency   string
			StartDate   string
			EndDate     string
			Notes       string
			CreatedAt   string
		}
		err := rows.Scan(&r.ID, &r.Description, &r.Amount, &r.Category, &r.Type, &r.Frequency, &r.StartDate, &r.EndDate, &r.Notes, &r.CreatedAt)
		if err != nil {
			c.J<PERSON>(http.StatusInternalServerError, gin.H{"error": "Database error"})
			return
		}
		recurring = append(recurring, gin.H{
			"id":          r.ID,
			"description": r.Description,
			"amount":      r.Amount,
			"category":    r.Category,
			"type":        r.Type,
			"frequency":   r.Frequency,
			"start_date":  r.StartDate,
			"end_date":    r.EndDate,
			"notes":       r.Notes,
			"created_at":  r.CreatedAt,
		})
	}
	c.JSON(http.StatusOK, gin.H{"recurring_transactions": recurring})
}
