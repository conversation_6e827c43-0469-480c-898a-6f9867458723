import 'dart:convert';
import 'package:securebooks/providers/auth_provider.dart';
import 'package:http/http.dart' as http;
import '../../models/budget.dart';

class BudgetApi {
  static const String baseUrl = 'http://localhost:8080/api/v1/budgets';

  Future<Map<String, String>> _getHeaders() async {
    final token = await AuthProvider.getToken();
    return {
      'Content-Type': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }

  Future<List<Budget>> fetchBudgets() async {
    final headers = await _getHeaders();
    final response = await http.get(Uri.parse(baseUrl), headers: headers);
    if (response.statusCode == 200) {
      final List data = json.decode(response.body);
      return data.map((e) => Budget.fromJson(e)).toList();
    } else {
      throw Exception('Failed to fetch budgets');
    }
  }

  Future<List<BudgetStatus>> fetchBudgetStatus() async {
    final headers = await _getHeaders();
    final response = await http.get(Uri.parse('$baseUrl/status'), headers: headers);
    if (response.statusCode == 200) {
      final List data = json.decode(response.body);
      return data.map((e) => BudgetStatus.fromJson(e)).toList();
    } else {
      throw Exception('Failed to fetch budget status');
    }
  }

  Future<void> addBudget(Budget budget) async {
    final headers = await _getHeaders();
    final response = await http.post(
      Uri.parse(baseUrl),
      headers: headers,
      body: json.encode(budget.toJson()),
    );
    if (response.statusCode != 201) {
      throw Exception('Failed to add budget');
    }
  }

  Future<void> updateBudget(Budget budget) async {
    final headers = await _getHeaders();
    final response = await http.put(
      Uri.parse('$baseUrl/${budget.id}'),
      headers: headers,
      body: json.encode(budget.toJson()),
    );
    if (response.statusCode != 200) {
      throw Exception('Failed to update budget');
    }
  }

  Future<void> deleteBudget(int id) async {
    final headers = await _getHeaders();
    final response = await http.delete(Uri.parse('$baseUrl/$id'), headers: headers);
    if (response.statusCode != 200) {
      throw Exception('Failed to delete budget');
    }
  }
}