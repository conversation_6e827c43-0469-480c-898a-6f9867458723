import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/analytics_provider.dart';

class AnalyticsDashboardScreen extends StatefulWidget {
  const AnalyticsDashboardScreen({super.key});

  @override
  State<AnalyticsDashboardScreen> createState() => _AnalyticsDashboardScreenState();
}

class _AnalyticsDashboardScreenState extends State<AnalyticsDashboardScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<AnalyticsProvider>(context, listen: false).fetchAll();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AnalyticsProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }
        if (provider.error != null) {
          return Center(child: Text('Error: [200m${provider.error}[0m'));
        }
        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _SpendingPatternWidget(data: provider.spendingPatterns),
              const SizedBox(height: 24),
              _ForecastWidget(data: provider.forecast),
              const SizedBox(height: 24),
              _AnomalyWidget(data: provider.anomalies),
              const SizedBox(height: 24),
              _HealthScoreWidget(data: provider.healthScore),
              const SizedBox(height: 24),
              _SavingsWidget(data: provider.savings),
            ],
          ),
        );
      },
    );
  }
  // Add this method to expose AppBar actions for HomeScreen
  List<Widget> buildAppBarActions(BuildContext context) => [];
}

class _SectionTitle extends StatelessWidget {
  final String title;
  const _SectionTitle(this.title);
  @override
  Widget build(BuildContext context) => Padding(
    padding: const EdgeInsets.only(bottom: 8),
    child: Text(title, style: Theme.of(context).textTheme.titleLarge),
  );
}

class _SpendingPatternWidget extends StatelessWidget {
  final List<Map<String, dynamic>> data;
  const _SpendingPatternWidget({required this.data});
  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Top Categories'),
            const SizedBox(height: 8),
            ...data.map((cat) => Text('${cat['name']}: ${cat['percent']}%')),
          ],
        ),
      ),
    );
  }
}

class _ForecastWidget extends StatelessWidget {
  final Map<String, dynamic>? data;
  const _ForecastWidget({required this.data});
  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Projected End-of-Month Balance'),
            const SizedBox(height: 8),
            Text('Expected: +\$${data!['projected_balance']}'),
            const SizedBox(height: 8),
            if (data!['forecast_by_category'] != null)
              ...List<Map<String, dynamic>>.from(data!['forecast_by_category']).map((cat) => Text('${cat['category']}: \$${cat['amount']}')),
          ],
        ),
      ),
    );
  }
}

class _AnomalyWidget extends StatelessWidget {
  final List<Map<String, dynamic>> data;
  const _AnomalyWidget({required this.data});
  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Unusual Transactions'),
            const SizedBox(height: 8),
            ...data.map((anomaly) => ListTile(
              leading: const Icon(Icons.warning, color: Colors.orange),
              title: Text(anomaly['description']),
              subtitle: Text('${anomaly['amount']} on ${anomaly['date']}'),
            )),
          ],
        ),
      ),
    );
  }
}

class _HealthScoreWidget extends StatelessWidget {
  final Map<String, dynamic>? data;
  const _HealthScoreWidget({required this.data});
  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Your Financial Health Score'),
            const SizedBox(height: 8),
            LinearProgressIndicator(value: (data!['score'] ?? 0) / (data!['max'] ?? 100), color: Colors.green),
            const SizedBox(height: 8),
            Text('Score: ${data!['score']}/${data!['max']}'),
            if (data!['message'] != null) Text(data!['message']),
          ],
        ),
      ),
    );
  }
}

class _SavingsWidget extends StatelessWidget {
  final Map<String, dynamic>? data;
  const _SavingsWidget({required this.data});
  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Savings & Investment Progress'),
            const SizedBox(height: 8),
            LinearProgressIndicator(
              value: (data!['current'] ?? 0) / ((data!['goal'] ?? 1) == 0 ? 1 : data!['goal']),
              color: Colors.blue,
            ),
            const SizedBox(height: 8),
            Text('Goal: \$${data!['goal']}'),
            Text('Current: \$${data!['current']}'),
            if (data!['investment_growth'] != null)
              Text('Investments up ${(data!['investment_growth'] * 100).toStringAsFixed(1)}% this month'),
          ],
        ),
      ),
    );
  }
}