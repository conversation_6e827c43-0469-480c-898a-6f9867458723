# SecureBooks Backend

## Database Setup

### 1. Create the Database Schema

Run the SQL commands in `schema.sql` in your PostgreSQL database:

```bash
# Connect to your PostgreSQL database
psql -U your_username -d your_database_name

# Run the schema file
\i schema.sql
```

Or copy and paste the contents of `schema.sql` into your database client (pgAdmin, DBeaver, etc.).

### 2. Required Tables

The schema creates the following tables:
- `users` - User accounts
- `invoices` - Invoice data
- `invoice_templates` - Invoice templates
- `transactions` - Financial transactions
- `recurring_transactions` - Recurring transactions
- `budgets` - Budget data

### 3. Required Sequence

- `invoice_number_seq` - For generating unique invoice numbers

### 4. Environment Variables

Make sure your `.env` file contains:

```env
DB_HOST=localhost
DB_PORT=5432
DB_USER=your_username
DB_PASSWORD=your_password
DB_NAME=your_database_name
JWT_SECRET=your_jwt_secret
DRAFT_EXPIRATION_DAYS=30
```

### 5. Run the Backend

```bash
cd backend
go run main.go
```

The server will start on `http://localhost:8080`

## API Endpoints

- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/login` - User login
- `GET /api/v1/invoices/` - Get invoices
- `POST /api/v1/invoices/` - Create invoice
- `GET /api/v1/templates/` - Get invoice templates
- `POST /api/v1/templates/` - Create invoice template

## Troubleshooting

### "Failed to get next invoice number" Error
- Make sure the `invoice_number_seq` sequence exists in your database
- Run: `CREATE SEQUENCE IF NOT EXISTS invoice_number_seq;`

### "Database error" on Templates
- Make sure the `invoice_templates` table exists
- Run the complete `schema.sql` file

### Connection Issues
- Check your database connection string in `.env`
- Ensure PostgreSQL is running
- Verify database credentials