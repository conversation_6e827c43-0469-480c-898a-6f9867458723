version: "3.9"

services:
  # ====================
  # PRODUCTION BACKEND
  # ====================
  backend:
    build: 
      context: ./backend
      dockerfile: ../Dockerfile
    container_name: securebooks_backend_prod
    restart: always
    ports:
      - "443:443"
      - "80:80"
    env_file:
      - .env.prod
    volumes:
      - /etc/letsencrypt:/etc/letsencrypt:ro
      - ./web/build:/app/web:ro
    depends_on:
      - postgres_prod
    networks:
      - securebooks_network

  # ====================
  # PRODUCTION DATABASE
  # ====================
  postgres_prod:
    image: postgres:17
    container_name: securebooks_db_prod
    restart: always
    env_file:
      - .env.prod
    environment:
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_DB: ${DB_NAME}
    ports:
      - "${DB_PORT}:5432"
    volumes:
      - postgres_prod_data:/var/lib/postgresql/data
    networks:
      - securebooks_network

volumes:
  postgres_prod_data:

networks:
  securebooks_network:
    driver: bridge