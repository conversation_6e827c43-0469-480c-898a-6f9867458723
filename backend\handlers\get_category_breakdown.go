package handlers

import (
	"database/sql"
	"net/http"

	"github.com/gin-gonic/gin"
)

func GetCategoryBreakdownHandler(c *gin.Context) {
	userID := c.GetInt("userID")
	db := c.MustGet("db").(*sql.DB)
	rows, err := db.Query("SELECT category, SUM(amount) FROM transactions WHERE user_id = $1 AND type = 'expense' GROUP BY category", userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error"})
		return
	}
	defer rows.Close()
	breakdown := map[string]float64{}
	for rows.Next() {
		var category string
		var sum float64
		err := rows.Scan(&category, &sum)
		if err != nil {
			continue
		}
		breakdown[category] = sum
	}
	c.JSON(http.StatusOK, gin.H{"category_breakdown": breakdown})
}
