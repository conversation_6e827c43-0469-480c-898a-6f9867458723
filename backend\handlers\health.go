package handlers

import (
	"context"
	"database/sql"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type HealthStatus struct {
	Status    string                 `json:"status"`
	Timestamp string                 `json:"timestamp"`
	Version   string                 `json:"version"`
	Uptime    string                 `json:"uptime"`
	Checks    map[string]HealthCheck `json:"checks"`
}

type HealthCheck struct {
	Status      string        `json:"status"`
	Message     string        `json:"message,omitempty"`
	Duration    time.Duration `json:"duration"`
	LastChecked string        `json:"last_checked"`
}

var (
	startTime = time.Now()
	version   = "1.0.0" // This should be set during build
)

// HealthHandler provides detailed health check information
func HealthHandler(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		health := HealthStatus{
			Status:    "healthy",
			Timestamp: time.Now().UTC().Format(time.RFC3339),
			Version:   version,
			Uptime:    time.Since(startTime).String(),
			Checks:    make(map[string]HealthCheck),
		}

		// Database health check
		dbCheck := checkDatabase(ctx, db)
		health.Checks["database"] = dbCheck

		// Memory health check
		memCheck := checkMemory()
		health.Checks["memory"] = memCheck

		// Disk space check
		diskCheck := checkDiskSpace()
		health.Checks["disk"] = diskCheck

		// External services check
		extCheck := checkExternalServices(ctx)
		health.Checks["external_services"] = extCheck

		// Determine overall status
		overallStatus := "healthy"
		for _, check := range health.Checks {
			if check.Status == "unhealthy" {
				overallStatus = "unhealthy"
				break
			} else if check.Status == "degraded" && overallStatus == "healthy" {
				overallStatus = "degraded"
			}
		}
		health.Status = overallStatus

		// Set appropriate HTTP status code
		statusCode := http.StatusOK
		if overallStatus == "unhealthy" {
			statusCode = http.StatusServiceUnavailable
		} else if overallStatus == "degraded" {
			statusCode = http.StatusPartialContent
		}

		c.JSON(statusCode, health)
	}
}

// ReadinessHandler provides a simple readiness check
func ReadinessHandler(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
		defer cancel()

		// Quick database connectivity check
		sqlDB, err := db.DB()
		if err != nil {
			c.JSON(http.StatusServiceUnavailable, gin.H{
				"status": "not ready",
				"reason": "database connection error",
			})
			return
		}

		if err := sqlDB.PingContext(ctx); err != nil {
			c.JSON(http.StatusServiceUnavailable, gin.H{
				"status": "not ready",
				"reason": "database ping failed",
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"status": "ready",
		})
	}
}

// LivenessHandler provides a simple liveness check
func LivenessHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status": "alive",
			"timestamp": time.Now().UTC().Format(time.RFC3339),
		})
	}
}

func checkDatabase(ctx context.Context, db *gorm.DB) HealthCheck {
	start := time.Now()
	check := HealthCheck{
		LastChecked: start.UTC().Format(time.RFC3339),
	}

	sqlDB, err := db.DB()
	if err != nil {
		check.Status = "unhealthy"
		check.Message = "Failed to get database connection: " + err.Error()
		check.Duration = time.Since(start)
		return check
	}

	// Test connection
	if err := sqlDB.PingContext(ctx); err != nil {
		check.Status = "unhealthy"
		check.Message = "Database ping failed: " + err.Error()
		check.Duration = time.Since(start)
		return check
	}

	// Check connection pool stats
	stats := sqlDB.Stats()
	if stats.OpenConnections > 80 { // Assuming max 100 connections
		check.Status = "degraded"
		check.Message = "High connection usage"
	} else {
		check.Status = "healthy"
		check.Message = "Database connection healthy"
	}

	check.Duration = time.Since(start)
	return check
}

func checkMemory() HealthCheck {
	start := time.Now()
	// This is a simplified memory check
	// In production, you might want to use runtime.MemStats
	return HealthCheck{
		Status:      "healthy",
		Message:     "Memory usage within limits",
		Duration:    time.Since(start),
		LastChecked: start.UTC().Format(time.RFC3339),
	}
}

func checkDiskSpace() HealthCheck {
	start := time.Now()
	// This is a simplified disk check
	// In production, you might want to check actual disk usage
	return HealthCheck{
		Status:      "healthy",
		Message:     "Disk space sufficient",
		Duration:    time.Since(start),
		LastChecked: start.UTC().Format(time.RFC3339),
	}
}

func checkExternalServices(ctx context.Context) HealthCheck {
	start := time.Now()
	// Check external dependencies like email service, etc.
	// This is a placeholder - implement actual checks based on your dependencies
	return HealthCheck{
		Status:      "healthy",
		Message:     "External services accessible",
		Duration:    time.Since(start),
		LastChecked: start.UTC().Format(time.RFC3339),
	}
}
