import 'package:sqflite/sqflite.dart' as sqflite;
import 'package:path/path.dart';
import '../models/transaction.dart';
import '../models/invoice.dart';

class DatabaseService {
  static sqflite.Database? _database;

  Future<sqflite.Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<sqflite.Database> _initDatabase() async {
    String path = join(await sqflite.getDatabasesPath(), 'securebooks.db');
    return await sqflite.openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
    );
  }

  Future<void> _onCreate(sqflite.Database db, int version) async {
    // Transactions table
    await db.execute('''
      CREATE TABLE transactions(
        id TEXT PRIMARY KEY,
        description TEXT NOT NULL,
        amount REAL NOT NULL,
        category TEXT NOT NULL,
        type TEXT NOT NULL,
        date TEXT NOT NULL,
        notes TEXT,
        isRecurring INTEGER DEFAULT 0,
        recurringId TEXT
      )
    ''');

    // Recurring transactions table
    await db.execute('''
      CREATE TABLE recurring_transactions(
        id TEXT PRIMARY KEY,
        description TEXT NOT NULL,
        amount REAL NOT NULL,
        category TEXT NOT NULL,
        type TEXT NOT NULL,
        frequency TEXT NOT NULL,
        startDate TEXT NOT NULL,
        endDate TEXT,
        notes TEXT
      )
    ''');
  }

  // Transaction operations
  Future<List<Transaction>> getTransactions() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('transactions');
    return List.generate(maps.length, (i) => Transaction.fromJson(maps[i]));
  }

  Future<Transaction> saveTransaction(Transaction transaction) async {
    final db = await database;
    await db.insert('transactions', transaction.toJson());
    return transaction;
  }

  Future<Transaction> updateTransaction(Transaction transaction) async {
    final db = await database;
    await db.update(
      'transactions',
      transaction.toJson(),
      where: 'id = ?',
      whereArgs: [transaction.id],
    );
    return transaction;
  }

  Future<void> deleteTransaction(String id) async {
    final db = await database;
    await db.delete(
      'transactions',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Recurring transaction operations
  Future<List<RecurringTransaction>> getRecurringTransactions() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('recurring_transactions');
    return List.generate(maps.length, (i) => RecurringTransaction.fromJson(maps[i]));
  }

  Future<RecurringTransaction> saveRecurringTransaction(RecurringTransaction recurring) async {
    final db = await database;
    await db.insert('recurring_transactions', recurring.toJson());
    return recurring;
  }

  Future<void> deleteRecurringTransaction(String id) async {
    final db = await database;
    await db.delete(
      'recurring_transactions',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // CSV Export
  Future<String> exportTransactionsToCSV() async {
    final transactions = await getTransactions();
    final csv = StringBuffer();
    csv.writeln('Date,Description,Category,Type,Amount,Notes');
    
    for (var transaction in transactions) {
      csv.writeln('${transaction.date.toIso8601String()},${transaction.description},${transaction.category},${transaction.type},${transaction.amount},${transaction.notes ?? ""}');
    }
    
    return csv.toString();
  }

  // Invoice operations
  Future<List<Invoice>> getInvoices() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('invoices');
    return List.generate(maps.length, (i) => Invoice.fromJson(maps[i]));
  }

  Future<Invoice> saveInvoice(Invoice invoice) async {
    final db = await database;
    await db.insert('invoices', invoice.toJson());
    return invoice;
  }

  Future<Invoice> updateInvoice(Invoice invoice) async {
    final db = await database;
    await db.update(
      'invoices',
      invoice.toJson(),
      where: 'id = ?',
      whereArgs: [invoice.id],
    );
    return invoice;
  }

  Future<void> deleteInvoice(String id) async {
    final db = await database;
    await db.delete(
      'invoices',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Invoice template operations
  Future<List<InvoiceTemplate>> getInvoiceTemplates() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('invoice_templates');
    return List.generate(maps.length, (i) => InvoiceTemplate.fromJson(maps[i]));
  }

  Future<InvoiceTemplate> saveInvoiceTemplate(InvoiceTemplate template) async {
    final db = await database;
    await db.insert('invoice_templates', template.toJson());
    return template;
  }

  Future<void> deleteInvoiceTemplate(String id) async {
    final db = await database;
    await db.delete(
      'invoice_templates',
      where: 'id = ?',
      whereArgs: [id],
    );
  }
}