package handlers

import (
	"database/sql"
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
)

func GetInvoicesHandler(c *gin.Context) {
	userID := c.GetInt("userID")
	db := c.MustGet("db").(*sql.DB)
	rows, err := db.Query("SELECT id, client_name, client_email, total, status, due_date, created_at FROM invoices WHERE user_id = $1 ORDER BY created_at DESC", userID)
	if err != nil {
		fmt.Printf("GetInvoicesHandler DB error: %v\n", err)
		c.<PERSON>(http.StatusInternalServerError, gin.H{"error": "Database error"})
		return
	}
	defer rows.Close()
	invoices := []gin.H{}
	for rows.Next() {
		var i struct {
			ID          int
			ClientName  string
			ClientEmail string
			Total       float64
			Status      string
			DueDate     string
			CreatedAt   string
		}
		err := rows.Scan(&i.ID, &i.ClientName, &i.ClientEmail, &i.Total, &i.Status, &i.DueDate, &i.CreatedAt)
		if err != nil {
			fmt.Printf("GetInvoicesHandler row scan error: %v\n", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error"})
			return
		}
		// Fetch items for this invoice
		itemRows, err := db.Query("SELECT id, description, quantity, price FROM invoice_items WHERE invoice_id = $1 ORDER BY id", i.ID)
		if err != nil {
			fmt.Printf("GetInvoicesHandler item query error: %v\n", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error (items)"})
			return
		}
		items := []gin.H{}
		for itemRows.Next() {
			var item struct {
				ID          int
				Description string
				Quantity    int
				Price       float64
			}
			if err := itemRows.Scan(&item.ID, &item.Description, &item.Quantity, &item.Price); err != nil {
				fmt.Printf("GetInvoicesHandler item scan error: %v\n", err)
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error (item scan)"})
				itemRows.Close()
				return
			}
			items = append(items, gin.H{
				"id":          item.ID,
				"description": item.Description,
				"quantity":    item.Quantity,
				"price":       item.Price,
			})
		}
		itemRows.Close()
		invoices = append(invoices, gin.H{
			"id":           i.ID,
			"client_name":  i.ClientName,
			"client_email": i.ClientEmail,
			"items":        items,
			"total":        i.Total,
			"status":       i.Status,
			"due_date":     i.DueDate,
			"created_at":   i.CreatedAt,
		})
	}
	c.JSON(http.StatusOK, gin.H{"invoices": invoices})
}
