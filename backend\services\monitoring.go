package services

import (
	"fmt"
	"log"
	"runtime"
	"sync"
	"time"

	"gorm.io/gorm"
)

type MonitoringService struct {
	db       *gorm.DB
	metrics  *SystemMetrics
	alerts   *AlertManager
	stopChan chan struct{}
	wg       sync.WaitGroup
}

type SystemMetrics struct {
	mu                  sync.RWMutex
	CPUUsage            float64
	MemoryUsage         float64
	GoroutineCount      int
	DatabaseConnections int
	RequestsPerSecond   float64
	ErrorRate           float64
	ResponseTime        time.Duration
	LastUpdated         time.Time
}

type AlertManager struct {
	mu     sync.RWMutex
	alerts []Alert
}

type Alert struct {
	ID         string     `json:"id"`
	Type       string     `json:"type"`
	Severity   string     `json:"severity"`
	Message    string     `json:"message"`
	Timestamp  time.Time  `json:"timestamp"`
	Resolved   bool       `json:"resolved"`
	ResolvedAt *time.Time `json:"resolved_at,omitempty"`
}

type AlertThresholds struct {
	CPUUsageThreshold     float64
	MemoryUsageThreshold  float64
	ErrorRateThreshold    float64
	ResponseTimeThreshold time.Duration
	DatabaseConnThreshold int
}

var defaultThresholds = AlertThresholds{
	CPUUsageThreshold:     80.0, // 80%
	MemoryUsageThreshold:  85.0, // 85%
	ErrorRateThreshold:    5.0,  // 5%
	ResponseTimeThreshold: 2 * time.Second,
	DatabaseConnThreshold: 80, // 80 connections
}

func NewMonitoringService(db *gorm.DB) *MonitoringService {
	return &MonitoringService{
		db: db,
		metrics: &SystemMetrics{
			LastUpdated: time.Now(),
		},
		alerts: &AlertManager{
			alerts: make([]Alert, 0),
		},
		stopChan: make(chan struct{}),
	}
}

func (ms *MonitoringService) Start() {
	ms.wg.Add(3)

	// Start metrics collection
	go ms.collectMetrics()

	// Start alert monitoring
	go ms.monitorAlerts()

	// Start cleanup routine
	go ms.cleanup()

	log.Println("Monitoring service started")
}

func (ms *MonitoringService) Stop() {
	close(ms.stopChan)
	ms.wg.Wait()
	log.Println("Monitoring service stopped")
}

func (ms *MonitoringService) collectMetrics() {
	defer ms.wg.Done()
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			ms.updateSystemMetrics()
		case <-ms.stopChan:
			return
		}
	}
}

func (ms *MonitoringService) updateSystemMetrics() {
	ms.metrics.mu.Lock()
	defer ms.metrics.mu.Unlock()

	// Update memory stats
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)

	ms.metrics.MemoryUsage = float64(memStats.Alloc) / float64(memStats.Sys) * 100
	ms.metrics.GoroutineCount = runtime.NumGoroutine()

	// Update database connection stats
	if sqlDB, err := ms.db.DB(); err == nil {
		stats := sqlDB.Stats()
		ms.metrics.DatabaseConnections = stats.OpenConnections
	}

	ms.metrics.LastUpdated = time.Now()
}

func (ms *MonitoringService) monitorAlerts() {
	defer ms.wg.Done()
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			ms.checkAlertConditions()
		case <-ms.stopChan:
			return
		}
	}
}

func (ms *MonitoringService) checkAlertConditions() {
	ms.metrics.mu.RLock()
	metrics := *ms.metrics
	ms.metrics.mu.RUnlock()

	// Check CPU usage
	if metrics.CPUUsage > defaultThresholds.CPUUsageThreshold {
		ms.createAlert("cpu_high", "warning",
			"High CPU usage detected: %.2f%%", metrics.CPUUsage)
	}

	// Check memory usage
	if metrics.MemoryUsage > defaultThresholds.MemoryUsageThreshold {
		ms.createAlert("memory_high", "warning",
			"High memory usage detected: %.2f%%", metrics.MemoryUsage)
	}

	// Check database connections
	if metrics.DatabaseConnections > defaultThresholds.DatabaseConnThreshold {
		ms.createAlert("db_connections_high", "warning",
			"High database connection count: %d", metrics.DatabaseConnections)
	}

	// Check error rate
	if metrics.ErrorRate > defaultThresholds.ErrorRateThreshold {
		ms.createAlert("error_rate_high", "critical",
			"High error rate detected: %.2f%%", metrics.ErrorRate)
	}

	// Check response time
	if metrics.ResponseTime > defaultThresholds.ResponseTimeThreshold {
		ms.createAlert("response_time_high", "warning",
			"High response time detected: %v", metrics.ResponseTime)
	}
}

func (ms *MonitoringService) createAlert(alertType, severity, message string, args ...interface{}) {
	ms.alerts.mu.Lock()
	defer ms.alerts.mu.Unlock()

	// Check if similar alert already exists and is not resolved
	for _, alert := range ms.alerts.alerts {
		if alert.Type == alertType && !alert.Resolved {
			return // Don't create duplicate alerts
		}
	}

	alert := Alert{
		ID:        generateAlertID(),
		Type:      alertType,
		Severity:  severity,
		Message:   fmt.Sprintf(message, args...),
		Timestamp: time.Now(),
		Resolved:  false,
	}

	ms.alerts.alerts = append(ms.alerts.alerts, alert)

	// Log the alert
	log.Printf("ALERT [%s]: %s", severity, alert.Message)

	// In production, you might want to send this to external alerting systems
	// like PagerDuty, Slack, email, etc.
}

func (ms *MonitoringService) cleanup() {
	defer ms.wg.Done()
	ticker := time.NewTicker(1 * time.Hour)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			ms.cleanupOldAlerts()
		case <-ms.stopChan:
			return
		}
	}
}

func (ms *MonitoringService) cleanupOldAlerts() {
	ms.alerts.mu.Lock()
	defer ms.alerts.mu.Unlock()

	cutoff := time.Now().Add(-24 * time.Hour)
	filteredAlerts := make([]Alert, 0)

	for _, alert := range ms.alerts.alerts {
		if alert.Timestamp.After(cutoff) {
			filteredAlerts = append(filteredAlerts, alert)
		}
	}

	ms.alerts.alerts = filteredAlerts
}

func (ms *MonitoringService) GetMetrics() SystemMetrics {
	ms.metrics.mu.RLock()
	defer ms.metrics.mu.RUnlock()
	return *ms.metrics
}

func (ms *MonitoringService) GetAlerts() []Alert {
	ms.alerts.mu.RLock()
	defer ms.alerts.mu.RUnlock()

	alerts := make([]Alert, len(ms.alerts.alerts))
	copy(alerts, ms.alerts.alerts)
	return alerts
}

func (ms *MonitoringService) ResolveAlert(alertID string) error {
	ms.alerts.mu.Lock()
	defer ms.alerts.mu.Unlock()

	for i := range ms.alerts.alerts {
		if ms.alerts.alerts[i].ID == alertID {
			now := time.Now()
			ms.alerts.alerts[i].Resolved = true
			ms.alerts.alerts[i].ResolvedAt = &now
			return nil
		}
	}

	return fmt.Errorf("alert with ID %s not found", alertID)
}

func generateAlertID() string {
	return fmt.Sprintf("alert_%d", time.Now().UnixNano())
}
