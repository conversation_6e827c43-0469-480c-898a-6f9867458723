package handlers

import (
	"database/sql"
	"net/http"

	"github.com/gin-gonic/gin"
)

func GetMonthlyTrendsHandler(c *gin.Context) {
	userID := c.GetInt("userID")
	db := c.MustGet("db").(*sql.DB)
	rows, err := db.Query(`SELECT to_char(date::date, 'YYYY-MM') as month, SUM(amount) FROM transactions WHERE user_id = $1 GROUP BY month ORDER BY month`, userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error"})
		return
	}
	defer rows.Close()
	trends := []gin.H{}
	for rows.Next() {
		var month string
		var sum float64
		err := rows.Scan(&month, &sum)
		if err != nil {
			continue
		}
		trends = append(trends, gin.H{"month": month, "total": sum})
	}
	c.<PERSON>(http.StatusOK, gin.H{"monthly_trends": trends})
}
