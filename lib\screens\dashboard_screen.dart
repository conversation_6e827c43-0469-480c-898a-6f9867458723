import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/expense_provider.dart';
import '../utils/theme.dart';
import 'home_screen.dart';

class DashboardScreen extends StatelessWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final expenseProvider = Provider.of<ExpenseProvider>(context);
    return Scaffold(
      // Remove the appBar property
      // appBar: AppBar(
      //   title: const Text('Dashboard'),
      // ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSummaryCards(expenseProvider),
            const SizedBox(height: 24),
            Text('Recent Transactions', style: Theme.of(context).textTheme.headlineSmall),
            const SizedBox(height: 8),
            _buildRecentTransactions(expenseProvider),
            const SizedBox(height: 24),
            _buildQuickLinks(context),
          ],
        ),
      ),
    );
  }

  // Add this method to expose AppBar actions for HomeScreen
  List<Widget> buildAppBarActions(BuildContext context) => [];

  Widget _buildSummaryCards(ExpenseProvider provider) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 4, // Four cards in a single row
      crossAxisSpacing: 8,
      mainAxisSpacing: 8,
      childAspectRatio: 0.85, // Make cards more square/compact
      children: [
        _buildSummaryCard(
          'Total Balance',
          '\$${provider.totalBalance.toStringAsFixed(2)}',
          provider.totalBalance >= 0 ? Colors.green : Colors.red,
          Icons.account_balance_wallet,
        ),
        _buildSummaryCard(
          'Total Income',
          '\$${provider.totalIncome.toStringAsFixed(2)}',
          Colors.green,
          Icons.trending_up,
        ),
        _buildSummaryCard(
          'Total Expenses',
          '\$${provider.totalExpenses.toStringAsFixed(2)}',
          Colors.red,
          Icons.trending_down,
        ),
        _buildSummaryCard(
          'Transactions',
          provider.transactions.length.toString(),
          AppTheme.primaryColor,
          Icons.receipt,
        ),
      ],
    );
  }

  Widget _buildSummaryCard(String title, String value, Color color, IconData icon) {
    return SizedBox(
      height: 36, // Half the previous height
      child: Card(
        elevation: 4,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 4),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Icon(icon, color: color, size: 28),
              const SizedBox(height: 2),
              Text(
                value,
                style: TextStyle(
                  color: color,
                  fontWeight: FontWeight.bold,
                  fontSize: 20,
                ),
                textAlign: TextAlign.center,
              ),
              Text(
                title,
                style: const TextStyle(color: Colors.grey, fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRecentTransactions(ExpenseProvider provider) {
    final recent = provider.transactions.take(5).toList();
    if (recent.isEmpty) {
      return const Text('No recent transactions.');
    }
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: recent.length,
      itemBuilder: (context, index) {
        final t = recent[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: t.type == 'income' ? Colors.green : Colors.red,
              child: Icon(
                t.type == 'income' ? Icons.trending_up : Icons.trending_down,
                color: Colors.white,
              ),
            ),
            title: Text(t.description),
            subtitle: Text('${t.category} • ${_formatDate(t.date)}'),
            trailing: Text(
              '\$${t.amount.toStringAsFixed(2)}',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: t.type == 'income' ? Colors.green : Colors.red,
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildQuickLinks(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildQuickLink(context, Icons.account_balance_wallet, 'Transactions', 1),
        _buildQuickLink(context, Icons.receipt_long, 'Invoices', 2),
        _buildQuickLink(context, Icons.person, 'Profile', 3),
      ],
    );
  }

  Widget _buildQuickLink(BuildContext context, IconData icon, String label, int tabIndex) {
    return GestureDetector(
      onTap: () {
        final homeState = context.findAncestorStateOfType<HomeScreenState>();
        if (homeState != null) {
          homeState.setTab(tabIndex);
        }
      },
      child: Column(
        children: [
          CircleAvatar(
            backgroundColor: AppTheme.primaryColor,
            child: Icon(icon, color: Colors.white),
          ),
          const SizedBox(height: 8),
          Text(label),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.month}/${date.day}/${date.year}';
  }
} 