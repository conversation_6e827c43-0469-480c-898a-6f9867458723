package handlers

import (
	"database/sql"
	"encoding/csv"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

func ExportTransactionsCSVHandler(c *gin.Context) {
	userID := c.GetInt("userID")
	db := c.MustGet("db").(*sql.DB)
	rows, err := db.Query("SELECT id, amount, category, description, date, created_at FROM transactions WHERE user_id = $1 ORDER BY date DESC", userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error"})
		return
	}
	defer rows.Close()
	c.<PERSON><PERSON>("Content-Type", "text/csv")
	c.<PERSON><PERSON>("Content-Disposition", "attachment; filename=transactions.csv")
	writer := csv.NewWriter(c.Writer)
	defer writer.Flush()
	writer.Write([]string{"ID", "Amount", "Category", "Description", "Date", "CreatedAt"})
	for rows.Next() {
		var id int
		var amount float64
		var category, description, date, createdAt string
		err := rows.Scan(&id, &amount, &category, &description, &date, &createdAt)
		if err != nil {
			continue
		}
		writer.Write([]string{
			strconv.Itoa(id),
			strconv.FormatFloat(amount, 'f', 2, 64),
			category,
			description,
			date,
			createdAt,
		})
	}
}
