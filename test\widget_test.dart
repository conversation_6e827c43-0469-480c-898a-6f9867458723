import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:securebooks/main.dart';

void main() {
  group('SecureBooks App Tests', () {
    testWidgets('App should build without errors', (WidgetTester tester) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(const SecureBooksApp());

      // Just pump once to avoid timer issues
      await tester.pump();

      // Verify that the app builds successfully
      expect(find.byType(MaterialApp), findsOneWidget);
    });

    testWidgets('App should show material app', (WidgetTester tester) async {
      await tester.pumpWidget(const SecureBooksApp());
      await tester.pump();

      // Should show MaterialApp
      expect(find.byType(MaterialApp), findsOneWidget);
    });

    testWidgets('App should have provider setup', (WidgetTester tester) async {
      await tester.pumpWidget(const SecureBooksApp());
      await tester.pump();

      // Verify providers are set up by checking MultiProvider exists
      expect(find.byType(MultiProvider), findsOneWidget);
    });
  });
}
