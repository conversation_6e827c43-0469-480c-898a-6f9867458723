import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/budget_provider.dart';
import '../models/budget.dart';
import '../providers/auth_provider.dart';
import '../utils/screen_with_actions.dart';

class BudgetScreen extends StatelessWidget implements ScreenWithActions {
  const BudgetScreen({super.key});

  static void showAddBudgetDialog(BuildContext context, {Budget? budget}) {
    showDialog(
      context: context,
      builder: (context) => AddEditBudgetDialog(budget: budget),
    );
  }

  @override
  List<Widget> buildAppBarActions(BuildContext context) => [
        ElevatedButton.icon(
          icon: const Icon(Icons.add),
          label: const Text('Add Budget'),
          onPressed: () => showAddBudgetDialog(context),
        ),
      ];

  @override
  Widget build(BuildContext context) {
    return Consumer<BudgetProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }
        return Scaffold(
          body: RefreshIndicator(
            onRefresh: () async => provider.loadBudgetsAndStatus(),
            child: ListView(
              padding: const EdgeInsets.all(16),
              children: [
                if (provider.error != null)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 16),
                    child: Text('Error: ${provider.error}', style: const TextStyle(color: Colors.red)),
                  ),
                ...provider.budgets.map((budget) => BudgetCard(budget: budget)),
                const SizedBox(height: 32),
                Text('Budget Status', style: Theme.of(context).textTheme.headlineSmall),
                const SizedBox(height: 16),
                ...provider.statuses.map((status) => BudgetStatusCard(status: status)),
              ],
            ),
          ),
        );
      },
    );
  }
}

class BudgetCard extends StatelessWidget {
  final Budget budget;
  const BudgetCard({super.key, required this.budget});
  @override
  Widget build(BuildContext context) {
    final provider = Provider.of<BudgetProvider>(context, listen: false);
    return Card(
      child: ListTile(
        title: Text(budget.category ?? 'Overall'),
        subtitle: Text('${budget.periodType} • ${budget.periodStart.toLocal().toString().split(" ")[0]}'),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () => BudgetScreen.showAddBudgetDialog(context, budget: budget),
            ),
            IconButton(
              icon: const Icon(Icons.delete, color: Colors.red),
              onPressed: () async {
                await provider.deleteBudget(budget.id);
              },
            ),
          ],
        ),
        leading: const Icon(Icons.savings),
        subtitleTextStyle: const TextStyle(fontSize: 13),
        titleTextStyle: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
      ),
    );
  }
}

class BudgetStatusCard extends StatelessWidget {
  final BudgetStatus status;
  const BudgetStatusCard({super.key, required this.status});
  @override
  Widget build(BuildContext context) {
    final percent = (status.percentUsed * 100).clamp(0, 999).toStringAsFixed(1);
    Color color;
    switch (status.alertLevel) {
      case 'over':
        color = Colors.red;
        break;
      case 'approaching':
        color = Colors.orange;
        break;
      default:
        color = Colors.green;
    }
    return Card(
      child: ListTile(
        title: Text(status.category ?? 'Overall'),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('${status.periodType} • ${status.periodStart.toLocal().toString().split(" ")[0]}'),
            Text('Budget: ${status.amount} | Actual: ${status.actual}'),
            LinearProgressIndicator(
              value: status.percentUsed > 1 ? 1 : status.percentUsed,
              color: color,
              backgroundColor: Colors.grey[200],
            ),
            Text('Used: $percent% • Alert: ${status.alertLevel}'),
          ],
        ),
      ),
    );
  }
}

class AddEditBudgetDialog extends StatefulWidget {
  final Budget? budget;
  const AddEditBudgetDialog({this.budget, super.key});
  @override
  State<AddEditBudgetDialog> createState() => _AddEditBudgetDialogState();
}

class _AddEditBudgetDialogState extends State<AddEditBudgetDialog> {
  final _formKey = GlobalKey<FormState>();
  String? _category;
  String _periodType = 'monthly';
  DateTime _periodStart = DateTime(DateTime.now().year, DateTime.now().month, 1);
  double _amount = 0;

  @override
  void initState() {
    super.initState();
    if (widget.budget != null) {
      _category = widget.budget!.category;
      _periodType = widget.budget!.periodType;
      _periodStart = widget.budget!.periodStart;
      _amount = widget.budget!.amount;
    }
  }

  @override
  Widget build(BuildContext context) {
    final provider = Provider.of<BudgetProvider>(context, listen: false);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    int userId = int.tryParse(authProvider.userId ?? '') ?? 0;
    return AlertDialog(
      title: Text(widget.budget == null ? 'Add Budget' : 'Edit Budget'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                initialValue: _category ?? '',
                decoration: const InputDecoration(labelText: 'Category (leave blank for overall)'),
                onChanged: (val) => _category = val.isEmpty ? null : val,
              ),
              DropdownButtonFormField<String>(
                value: _periodType,
                decoration: const InputDecoration(labelText: 'Period Type'),
                items: const [
                  DropdownMenuItem(value: 'monthly', child: Text('Monthly')),
                  DropdownMenuItem(value: 'yearly', child: Text('Yearly')),
                ],
                onChanged: (val) => setState(() => _periodType = val!),
              ),
              TextFormField(
                initialValue: _amount == 0 ? '' : _amount.toString(),
                decoration: const InputDecoration(labelText: 'Amount'),
                keyboardType: TextInputType.number,
                validator: (val) {
                  final d = double.tryParse(val ?? '');
                  if (d == null || d <= 0) return 'Enter a positive amount';
                  return null;
                },
                onChanged: (val) => _amount = double.tryParse(val) ?? 0,
              ),
              ListTile(
                title: const Text('Period Start'),
                subtitle: Text(_periodStart.toLocal().toString().split(' ')[0]),
                trailing: IconButton(
                  icon: const Icon(Icons.calendar_today),
                  onPressed: () async {
                    final picked = await showDatePicker(
                      context: context,
                      initialDate: _periodStart,
                      firstDate: DateTime(2000),
                      lastDate: DateTime(2100),
                    );
                    if (picked != null) setState(() => _periodStart = picked);
                  },
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () async {
            if (_formKey.currentState!.validate()) {
              final budget = Budget(
                id: widget.budget?.id ?? 0,
                userId: userId,
                category: _category,
                periodType: _periodType,
                periodStart: _periodStart,
                amount: _amount,
              );
              if (widget.budget == null) {
                await provider.addBudget(budget);
              } else {
                await provider.updateBudget(budget);
              }
              if (context.mounted) Navigator.of(context).pop();
            }
          },
          child: Text(widget.budget == null ? 'Add' : 'Save'),
        ),
      ],
    );
  }
}