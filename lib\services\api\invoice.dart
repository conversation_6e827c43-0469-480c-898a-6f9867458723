import 'dart:convert';
import 'package:http/http.dart' as http;
import '../../models/invoice.dart';

class InvoiceApi {
  static const String baseUrl = 'http://localhost:8080/api/v1';
  String? _authToken;
  void Function()? onUnauthorized;

  void setAuthToken(String token) {
    _authToken = token;
  }

  void setOnUnauthorized(void Function() callback) {
    onUnauthorized = callback;
  }

  Map<String, String> get _headers {
    final headers = {
      'Content-Type': 'application/json',
    };
    if (_authToken != null) {
      headers['Authorization'] = 'Bearer $_authToken';
    }
    return headers;
  }

  void _handleUnauthorized() {
    if (onUnauthorized != null) {
      onUnauthorized!();
    }
  }

  Future<List<Invoice>> getInvoices() async {
    final response = await http.get(
      Uri.parse('$baseUrl/invoices/'),
      headers: _headers,
    );
    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      if (data is Map && data['invoices'] is List) {
        return (data['invoices'] as List)
            .map((i) => Invoice.fromJson(i))
            .toList();
      } else if (data is List) {
        return data.map<Invoice>((i) => Invoice.fromJson(i)).toList();
      } else {
        throw Exception('Unexpected response format');
      }
    } else if (response.statusCode == 401) {
      _handleUnauthorized();
      throw Exception('Unauthorized');
    } else {
      throw Exception('Failed to load invoices:  $response.body');
    }
  }

  Future<Invoice> createInvoice(Invoice invoice) async {
    // Prepare the payload in the format expected by the backend
    final Map<String, dynamic> payload = {
      'invoice_number': invoice.invoiceNumber,
      'date': invoice.date.toIso8601String(),
      'due_date': invoice.dueDate.toIso8601String(),
      'status': invoice.status,
      'tax_rate': invoice.taxRate,
      'currency': invoice.currency,
      'notes': invoice.notes,
      'client_name': invoice.client.name,
      'client_email': invoice.client.email,
      'items': invoice.items.map((i) => i.toJson()).toList(), // as list
      'total': invoice.total,
    };
    print('[InvoiceApi] POST $baseUrl/invoices/');
    print('[InvoiceApi] Headers:  $_headers');
    print('[InvoiceApi] Payload: $payload');
    final response = await http.post(
      Uri.parse('$baseUrl/invoices/'),
      headers: _headers,
      body: json.encode(payload),
    );
    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return Invoice.fromJson(data);
    } else if (response.statusCode == 401) {
      _handleUnauthorized();
      throw Exception('Unauthorized');
    } else {
      throw Exception('Failed to create invoice:  ${response.body}');
    }
  }

  Future<Invoice> createDraftInvoice(String userId) async {
    final now = DateTime.now();
    final payload = {
      'invoice_number': '',
      'date': now.toIso8601String(),
      'due_date': now.add(const Duration(days: 30)).toIso8601String(),
      'status': 'draft',
      'tax_rate': 0.0,
      'currency': 'USD',
      'notes': null,
      'client_name': '',
      'client_email': '',
      'items': [],
      'total': 0.0,
    };
    final response = await http.post(
      Uri.parse('$baseUrl/invoices/'),
      headers: _headers,
      body: json.encode(payload),
    );
    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return Invoice.fromJson(data);
    } else if (response.statusCode == 401) {
      _handleUnauthorized();
      throw Exception('Unauthorized');
    } else {
      throw Exception('Failed to create draft invoice:  ${response.body}');
    }
  }

  Future<void> deleteInvoice(String id) async {
    final response = await http.delete(
      Uri.parse('$baseUrl/invoices/$id'),
      headers: _headers,
    );
    if (response.statusCode == 200) {
      return;
    } else if (response.statusCode == 401) {
      _handleUnauthorized();
      throw Exception('Unauthorized');
    } else {
      throw Exception('Failed to delete invoice:  ${response.body}');
    }
  }

  Future<void> syncInvoices(List<Invoice> invoices) async {
    final response = await http.post(
      Uri.parse('$baseUrl/sync/invoices'),
      headers: _headers,
      body: json.encode({
        'invoices': invoices.map((i) => i.toJson()).toList(),
      }),
    );

    if (response.statusCode == 401) {
      _handleUnauthorized();
      throw Exception('Unauthorized');
    }
    if (response.statusCode != 200) {
      throw Exception('Sync failed:  $response.body');
    }
  }

  Future<void> syncInvoiceTemplates(List<InvoiceTemplate> templates) async {
    final response = await http.post(
      Uri.parse('$baseUrl/sync/templates'),
      headers: _headers,
      body: json.encode({
        'templates': templates.map((t) => t.toJson()).toList(),
      }),
    );

    if (response.statusCode == 401) {
      _handleUnauthorized();
      throw Exception('Unauthorized');
    }
    if (response.statusCode != 200) {
      throw Exception('Sync failed:  $response.body');
    }
  }

  Future<Invoice> getInvoiceById(String id) async {
    final response = await http.get(
      Uri.parse('$baseUrl/invoices/$id'),
      headers: _headers,
    );
    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return Invoice.fromJson(data);
    } else if (response.statusCode == 401) {
      _handleUnauthorized();
      throw Exception('Unauthorized');
    } else {
      throw Exception('Failed to fetch invoice:  ${response.body}');
    }
  }
} 