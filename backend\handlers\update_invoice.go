package handlers

import (
	"database/sql"
	"fmt"
	"log"
	"net/http"

	"github.com/gin-gonic/gin"
)

func UpdateInvoiceHandler(c *gin.Context) {
	userID := c.GetInt("userID")
	id := c.Param("id")
	log.Printf("UpdateInvoiceHandler: userID=%v, id=%v", userID, id)
	var req struct {
		ClientName  string `json:"client_name"`
		ClientEmail string `json:"client_email"`
		Items       []struct {
			Description string  `json:"description"`
			Quantity    int     `json:"quantity"`
			Price       float64 `json:"price"`
		} `json:"items"`
		Total   float64 `json:"total"`
		Status  string  `json:"status"`
		DueDate string  `json:"due_date"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request"})
		return
	}
	log.Printf("UpdateInvoiceHandler: status=%v", req.Status)
	// Fetch current status and invoice_number
	db := c.MustGet("db").(*sql.DB)
	var currentStatus, currentInvoiceNumber string
	err := db.QueryRow("SELECT status, invoice_number FROM invoices WHERE id = $1 AND user_id = $2", id, userID).Scan(&currentStatus, &currentInvoiceNumber)
	if err != nil {
		log.Printf("UpdateInvoiceHandler: invoice not found for id=%v, userID=%v", id, userID)
		c.JSON(http.StatusNotFound, gin.H{"error": "Invoice not found"})
		return
	}
	tx, err := db.Begin()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to start transaction"})
		return
	}
	// If finalizing (draft -> sent) and invoice_number is NULL, assign next invoice number
	if req.Status == "sent" && currentStatus == "draft" && (currentInvoiceNumber == "" || currentInvoiceNumber == "NULL") {
		var nextNumber int
		err := tx.QueryRow("SELECT nextval('invoice_number_seq')").Scan(&nextNumber)
		if err != nil {
			tx.Rollback()
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get next invoice number"})
			return
		}
		invoiceNumber := fmt.Sprintf("%05d", nextNumber)
		_, err = tx.Exec("UPDATE invoices SET invoice_number = $1, status = 'unpaid' WHERE id = $2 AND user_id = $3", invoiceNumber, id, userID)
		if err != nil {
			tx.Rollback()
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to assign invoice number"})
			return
		}
		log.Printf("UpdateInvoiceHandler: assigned invoice_number=%v to id=%v", invoiceNumber, id)
	}
	// If status is being set to 'sent', change to 'unpaid'
	statusToSet := req.Status
	if req.Status == "sent" {
		statusToSet = "unpaid"
	}
	log.Printf("UpdateInvoiceHandler: updating invoice id=%v, userID=%v, status=%v", id, userID, statusToSet)
	res, err := tx.Exec("UPDATE invoices SET client_name = $1, client_email = $2, total = $3, status = $4, due_date = $5 WHERE id = $6 AND user_id = $7", req.ClientName, req.ClientEmail, req.Total, statusToSet, req.DueDate, id, userID)
	if err != nil {
		tx.Rollback()
		log.Printf("UpdateInvoiceHandler: failed to update invoice id=%v, userID=%v: %v", id, userID, err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update invoice"})
		return
	}
	n, _ := res.RowsAffected()
	if n == 0 {
		tx.Rollback()
		log.Printf("UpdateInvoiceHandler: no rows affected for id=%v, userID=%v", id, userID)
		c.JSON(http.StatusNotFound, gin.H{"error": "Invoice not found"})
		return
	}
	// Delete existing items
	_, err = tx.Exec("DELETE FROM invoice_items WHERE invoice_id = $1", id)
	if err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete old items"})
		return
	}
	// Remove json.Unmarshal, use req.Items directly
	for _, item := range req.Items {
		_, err := tx.Exec("INSERT INTO invoice_items (invoice_id, description, quantity, price) VALUES ($1, $2, $3, $4)", id, item.Description, item.Quantity, item.Price)
		if err != nil {
			tx.Rollback()
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to insert invoice item"})
			return
		}
	}
	if err := tx.Commit(); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to commit invoice update"})
		return
	}
	log.Printf("UpdateInvoiceHandler: successfully updated invoice id=%v, userID=%v", id, userID)
	c.JSON(http.StatusOK, gin.H{"message": "Invoice updated"})
}
