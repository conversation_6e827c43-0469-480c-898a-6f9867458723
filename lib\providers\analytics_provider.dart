import 'package:flutter/material.dart';
import '../services/api/analytics_api.dart';

class AnalyticsProvider with ChangeNotifier {
  final AnalyticsApi api;
  bool isLoading = false;
  String? error;

  // All data is now loaded from the backend via AnalyticsApi. No mock data remains.
  List<Map<String, dynamic>> spendingPatterns = [];
  Map<String, dynamic>? forecast;
  List<Map<String, dynamic>> anomalies = [];
  Map<String, dynamic>? healthScore;
  Map<String, dynamic>? savings;

  AnalyticsProvider({required this.api});

  Future<void> fetchAll() async {
    isLoading = true;
    error = null;
    notifyListeners();
    try {
      spendingPatterns = await api.fetchSpendingPatterns();
      forecast = await api.fetchForecast();
      anomalies = await api.fetchAnomalies();
      healthScore = await api.fetchHealthScore();
      savings = await api.fetchSavings();
      error = null;
    } catch (e) {
      error = e.toString();
    } finally {
      isLoading = false;
      notifyListeners();
    }
  }
} 