import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'screens/home_screen.dart' as home_screen;
import 'providers/expense_provider.dart';
import 'providers/invoice_provider.dart';
import 'providers/auth_provider.dart';
import 'providers/theme_provider.dart';
import 'utils/theme.dart';
import 'screens/login_screen.dart';
import 'screens/registration_screen.dart';
import 'screens/splash_screen.dart';
import 'services/api/auth.dart';
import 'services/api/transaction.dart';
import 'services/api/invoice.dart';
import 'providers/notification_provider.dart';
import 'providers/sync_provider.dart';
import 'providers/analytics_provider.dart';
import 'providers/budget_provider.dart';
import 'services/api/budget_api.dart';
import 'services/api/analytics_api.dart';

void main() {
  runApp(const SecureBooksApp());
}

class SecureBooksApp extends StatelessWidget {
  const SecureBooksApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider(apiService: AuthApi())..initialize()),
        ChangeNotifierProxyProvider<AuthProvider, ExpenseProvider>(
          create: (context) => ExpenseProvider(apiService: TransactionApi()),
          update: (context, authProvider, previous) {
            final expenseProvider = previous ?? ExpenseProvider(apiService: TransactionApi());
            if (authProvider.token != null) {
              expenseProvider.apiService.setAuthToken(authProvider.token!);
            }
            return expenseProvider;
          },
        ),
        ChangeNotifierProxyProvider<AuthProvider, InvoiceProvider>(
          create: (context) => InvoiceProvider(apiService: InvoiceApi()),
          update: (context, authProvider, previous) {
            final invoiceProvider = previous ?? InvoiceProvider(apiService: InvoiceApi());
            if (authProvider.token != null) {
              invoiceProvider.apiService.setAuthToken(authProvider.token!);
            }
            return invoiceProvider;
          },
        ),
        ChangeNotifierProvider(create: (_) => ThemeProvider()),
        ChangeNotifierProvider(create: (_) => NotificationProvider()),
        ChangeNotifierProvider(create: (_) => SyncProvider()),
        ChangeNotifierProvider(
          create: (_) => AnalyticsProvider(api: AnalyticsApi()),
        ),
        ChangeNotifierProvider(
          create: (_) => BudgetProvider(api: BudgetApi()),
        ),
      ],
      child: Consumer<ThemeProvider>(
        builder: (context, themeProvider, _) => MaterialApp(
          title: 'SecureBooks',
          theme: AppTheme.lightTheme,
          darkTheme: AppTheme.darkTheme,
          themeMode: themeProvider.themeMode,
          debugShowCheckedModeBanner: false,
          home: const SplashOrAuthGate(),
        ),
      ),
    );
  }
}

class AuthGate extends StatefulWidget {
  const AuthGate({super.key});

  @override
  State<AuthGate> createState() => _AuthGateState();
}

class _AuthGateState extends State<AuthGate> {
  bool showLogin = true;

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    if (authProvider.isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }
    if (authProvider.isAuthenticated) {
      return const home_screen.HomeScreen();
    }
    return showLogin
        ? LoginScreen(onSwitchToRegister: () => setState(() => showLogin = false))
        : RegistrationScreen(onSwitchToLogin: () => setState(() => showLogin = true));
  }
}

class SplashOrAuthGate extends StatefulWidget {
  const SplashOrAuthGate({super.key});

  @override
  State<SplashOrAuthGate> createState() => _SplashOrAuthGateState();
}

class _SplashOrAuthGateState extends State<SplashOrAuthGate> {
  bool _showSplash = true;

  @override
  void initState() {
    super.initState();
    Future.delayed(const Duration(milliseconds: 1500), () {
      if (mounted) setState(() => _showSplash = false);
    });
  }

  @override
  Widget build(BuildContext context) {
    return _showSplash ? const SplashScreen() : const AuthGate();
  }
}