package handlers

import (
	"database/sql"
	"fmt"
	"log"
	"net/http"

	"github.com/gin-gonic/gin"
)

func CreateInvoiceHandler(c *gin.Context) {
	log.Println("CreateInvoiceHandler called")
	header := c.<PERSON><PERSON>("Authorization")
	log.Printf("Authorization header: %s", header)
	userID := c.GetInt("userID")
	log.Printf("userID: %v", userID)
	var req struct {
		InvoiceNumber string  `json:"invoice_number"`
		Date          string  `json:"date"`
		DueDate       string  `json:"due_date"`
		Status        string  `json:"status"`
		TaxRate       float64 `json:"tax_rate"`
		Currency      string  `json:"currency"`
		Notes         *string `json:"notes"`
		ClientName    string  `json:"client_name"`
		ClientEmail   string  `json:"client_email"`
		Items         []struct {
			Description string  `json:"description"`
			Quantity    int     `json:"quantity"`
			Price       float64 `json:"price"`
		} `json:"items"`
		Total float64 `json:"total"`
	}
	if err := c.<PERSON>(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request"})
		return
	}
	log.Printf("Request body: %+v", req)
	currency := req.Currency
	if currency == "" {
		currency = "USD"
	}
	// Use 'temporary' status if not explicitly set to 'draft' or 'unpaid' or 'paid' etc.
	status := req.Status
	if status == "" {
		status = "temporary"
	}
	var id int
	var createdDate string
	db := c.MustGet("db").(*sql.DB)
	tx, err := db.Begin()
	if err != nil {
		log.Printf("DB error: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create invoice (tx)"})
		return
	}
	err = tx.QueryRow(
		"INSERT INTO invoices (user_id, invoice_number, date, due_date, status, tax_rate, currency, notes, client_name, client_email, total) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11) RETURNING id, date",
		userID, nil, req.Date, req.DueDate, status, req.TaxRate, currency, req.Notes, req.ClientName, req.ClientEmail, req.Total,
	).Scan(&id, &createdDate)
	if err != nil {
		tx.Rollback()
		log.Printf("DB error (insert invoice): %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create invoice", "details": err.Error()})
		return
	}
	// Generate next invoice number (simple incrementing sequence, zero-padded)
	var nextNumber int
	err = tx.QueryRow("SELECT nextval('invoice_number_seq')").Scan(&nextNumber)
	if err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get next invoice number"})
		return
	}
	invoiceNumber := fmt.Sprintf("%05d", nextNumber)
	_, err = tx.Exec("UPDATE invoices SET invoice_number = $1 WHERE id = $2", invoiceNumber, id)
	if err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to assign invoice number"})
		return
	}
	for _, item := range req.Items {
		_, err := tx.Exec("INSERT INTO invoice_items (invoice_id, description, quantity, price) VALUES ($1, $2, $3, $4)", id, item.Description, item.Quantity, item.Price)
		if err != nil {
			tx.Rollback()
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to insert invoice item"})
			return
		}
	}
	if err := tx.Commit(); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to commit invoice creation"})
		return
	}
	// Query the full invoice (including items)
	var invoice struct {
		ID            int     `json:"id"`
		UserID        int     `json:"user_id"`
		InvoiceNumber string  `json:"invoice_number"`
		Date          string  `json:"date"`
		DueDate       string  `json:"due_date"`
		Status        string  `json:"status"`
		TaxRate       float64 `json:"tax_rate"`
		Currency      string  `json:"currency"`
		Notes         *string `json:"notes"`
		ClientName    string  `json:"client_name"`
		ClientEmail   string  `json:"client_email"`
		Total         float64 `json:"total"`
		Items         []gin.H `json:"items"`
	}
	err = db.QueryRow("SELECT id, user_id, invoice_number, date, due_date, status, tax_rate, currency, notes, client_name, client_email, total FROM invoices WHERE id = $1", id).Scan(
		&invoice.ID, &invoice.UserID, &invoice.InvoiceNumber, &invoice.Date, &invoice.DueDate, &invoice.Status, &invoice.TaxRate, &invoice.Currency, &invoice.Notes, &invoice.ClientName, &invoice.ClientEmail, &invoice.Total,
	)
	if err != nil {
		log.Printf("DB error (fetch invoice): %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch invoice", "details": err.Error()})
		return
	}
	itemRows, err := db.Query("SELECT id, description, quantity, price FROM invoice_items WHERE invoice_id = $1 ORDER BY id", id)
	if err != nil {
		log.Printf("DB error (fetch invoice items): %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch invoice items", "details": err.Error()})
		return
	}
	defer itemRows.Close()
	invoice.Items = []gin.H{}
	for itemRows.Next() {
		var item struct {
			ID          int     `json:"id"`
			Description string  `json:"description"`
			Quantity    int     `json:"quantity"`
			Price       float64 `json:"price"`
		}
		if err := itemRows.Scan(&item.ID, &item.Description, &item.Quantity, &item.Price); err != nil {
			log.Printf("DB error (scan invoice item): %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to scan invoice item", "details": err.Error()})
			return
		}
		invoice.Items = append(invoice.Items, gin.H{
			"id":          item.ID,
			"description": item.Description,
			"quantity":    item.Quantity,
			"price":       item.Price,
		})
	}
	c.JSON(http.StatusOK, invoice)
}
