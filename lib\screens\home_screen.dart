import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../providers/expense_provider.dart';
import '../providers/invoice_provider.dart';
import 'expense_screen.dart';
import 'invoice_screen.dart';
import 'profile_screen.dart';
import '../utils/theme.dart';
import 'dashboard_screen.dart';
import 'analytics_dashboard_screen.dart';
import 'budget_screen.dart';

// Add an interface for screens with actions
abstract class ScreenWithActions {
  List<Widget> buildAppBarActions(BuildContext context);
}

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _currentIndex = 0;

  // Use GlobalKeys to access stateful methods for dialogs
  final GlobalKey<InvoiceScreenState> _invoiceKey = GlobalKey<InvoiceScreenState>();
  final GlobalKey<ExpenseScreenState> _expenseKey = GlobalKey<ExpenseScreenState>();
  final GlobalKey _budgetKey = GlobalKey();

  void _onExpenseStateReady() {
    setState(() {});
  }
  void _onInvoiceStateReady() {
    setState(() {});
  }

  late final List<Widget> _screens = [
    const DashboardScreen(),
    ExpenseScreen(key: _expenseKey, onStateReady: _onExpenseStateReady),
    InvoiceScreen(key: _invoiceKey, onStateReady: _onInvoiceStateReady),
    BudgetScreen(key: _budgetKey),
    const AnalyticsDashboardScreen(),
    const ProfileScreen(),
  ];

  final List<NavigationDestination> _destinations = [
    const NavigationDestination(icon: Icon(Icons.dashboard), label: 'Dashboard'),
    const NavigationDestination(icon: Icon(Icons.account_balance_wallet), label: 'Transactions'),
    const NavigationDestination(icon: Icon(Icons.receipt_long), label: 'Invoices'),
    const NavigationDestination(icon: Icon(Icons.savings), label: 'Budgets'),
    const NavigationDestination(icon: Icon(Icons.analytics), label: 'Analytics'),
    const NavigationDestination(icon: Icon(Icons.person), label: 'Profile'),
  ];

  void setTab(int index) {
    setState(() {
      _currentIndex = index;
    });
  }

  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    await authProvider.initialize();
    
    if (authProvider.isAuthenticated) {
      final expenseProvider = Provider.of<ExpenseProvider>(context, listen: false);
      final invoiceProvider = Provider.of<InvoiceProvider>(context, listen: false);
      
      await expenseProvider.loadTransactions(authProvider: authProvider);
      await invoiceProvider.loadInvoices(authProvider: authProvider);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        if (authProvider.isLoading) {
          return const Scaffold(
            body: Center(
              child: CircularProgressIndicator(),
            ),
          );
        }

        if (!authProvider.isAuthenticated) {
          // Should never happen: authentication is handled in main.dart
          return const SizedBox.shrink();
        }

        return LayoutBuilder(
          builder: (context, constraints) {
            final isWide = constraints.maxWidth >= 600;
            final currentScreen = _screens[_currentIndex];
            // Get actions from the current screen if it has buildAppBarActions
            List<Widget> actions = [];
            if (_currentIndex == 1 && _expenseKey.currentState != null) {
              actions = _expenseKey.currentState!.buildAppBarActions(context);
            } else if (_currentIndex == 2 && _invoiceKey.currentState != null) {
              actions = _invoiceKey.currentState!.buildAppBarActions(context);
            } else if (_currentIndex == 3 && _screens[3] is BudgetScreen) {
              actions = (_screens[3] as BudgetScreen).buildAppBarActions(context);
            } else if (currentScreen is ScreenWithActions) {
              actions = (currentScreen as ScreenWithActions).buildAppBarActions(context);
            }
            // Add the plus button as a PopupMenuButton
            // The plus button is now handled by the individual screens
            if (isWide) {
              // Use NavigationRail for wide screens
              return Row(
                children: [
                  NavigationRail(
                    selectedIndex: _currentIndex,
                    onDestinationSelected: (index) {
                      setState(() {
                        _currentIndex = index;
                      });
                    },
                    labelType: NavigationRailLabelType.all,
                    destinations: const [
                      NavigationRailDestination(
                        icon: Icon(Icons.dashboard),
                        label: Text('Dashboard'),
                      ),
                      NavigationRailDestination(
                        icon: Icon(Icons.account_balance_wallet),
                        label: Text('Transactions'),
                      ),
                      NavigationRailDestination(
                        icon: Icon(Icons.receipt_long),
                        label: Text('Invoices'),
                      ),
                      NavigationRailDestination(
                        icon: Icon(Icons.savings),
                        label: Text('Budgets'),
                      ),
                      NavigationRailDestination(
                        icon: Icon(Icons.analytics),
                        label: Text('Analytics'),
                      ),
                      NavigationRailDestination(
                        icon: Icon(Icons.person),
                        label: Text('Profile'),
                      ),
                    ],
                  ),
                  const VerticalDivider(thickness: 1, width: 1),
                  Expanded(
                    child: Scaffold(
                      appBar: AppBar(
                        title: Text(_destinations[_currentIndex].label),
                        actions: actions,
                      ),
                      body: currentScreen,
                    ),
                  ),
                ],
              );
            } else {
              // Use BottomNavigationBar for mobile
              return Scaffold(
                appBar: AppBar(
                  title: Text(_destinations[_currentIndex].label),
                  actions: actions,
                ),
                body: currentScreen,
                bottomNavigationBar: BottomNavigationBar(
                  currentIndex: _currentIndex,
                  onTap: (index) {
                    setState(() {
                      _currentIndex = index;
                    });
                  },
                  type: BottomNavigationBarType.fixed,
                  backgroundColor: Theme.of(context).colorScheme.surface,
                  selectedItemColor: AppTheme.primaryColor,
                  unselectedItemColor: Colors.grey,
                  items: const [
                    BottomNavigationBarItem(
                      icon: Icon(Icons.dashboard),
                      label: 'Dashboard',
                    ),
                    BottomNavigationBarItem(
                      icon: Icon(Icons.account_balance_wallet),
                      label: 'Transactions',
                    ),
                    BottomNavigationBarItem(
                      icon: Icon(Icons.receipt_long),
                      label: 'Invoices',
                    ),
                    BottomNavigationBarItem(
                      icon: Icon(Icons.savings),
                      label: 'Budgets',
                    ),
                    BottomNavigationBarItem(
                      icon: Icon(Icons.analytics),
                      label: 'Analytics',
                    ),
                    BottomNavigationBarItem(
                      icon: Icon(Icons.person),
                      label: 'Profile',
                    ),
                  ],
                ),
              );
            }
          },
        );
      },
    );
  }

  Widget _buildActionPickerItem(BuildContext context, IconData icon, String label, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 22),
            const SizedBox(width: 12),
            Text(label, style: const TextStyle(fontSize: 16)),
          ],
        ),
      ),
    );
  }

  void _removeCreationOverlay() {
    // This method is no longer needed as _creationOverlay is removed
  }
} 

typedef HomeScreenState = _HomeScreenState; 

// Remove dummy creation dialogs from HomeScreen (delete InvoiceCreationDialog, TransactionCreationDialog, BudgetCreationDialog classes)
// The floating action picker should only call the real dialog methods via GlobalKey