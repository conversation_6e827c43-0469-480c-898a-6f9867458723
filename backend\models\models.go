package models

import (
	"time"

	"gorm.io/gorm"
)

// User represents a user in the system
type User struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	Email     string         `json:"email" gorm:"uniqueIndex;not null"`
	Password  string         `json:"-" gorm:"not null"`
	Name      string         `json:"name" gorm:"not null"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	Transactions          []Transaction          `json:"transactions,omitempty"`
	RecurringTransactions []RecurringTransaction `json:"recurring_transactions,omitempty"`
	Invoices              []Invoice              `json:"invoices,omitempty"`
	InvoiceTemplates      []InvoiceTemplate      `json:"invoice_templates,omitempty"`
}

// RefreshToken represents a refresh token for a user
type RefreshToken struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	UserID    uint           `json:"user_id" gorm:"not null;index"`
	Token     string         `json:"token" gorm:"uniqueIndex;not null"`
	ExpiresAt time.Time      `json:"expires_at" gorm:"not null"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// PasswordResetToken represents a password reset token for a user
type PasswordResetToken struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	UserID    uint           `json:"user_id" gorm:"not null;index"`
	Token     string         `json:"token" gorm:"uniqueIndex;not null"`
	ExpiresAt time.Time      `json:"expires_at" gorm:"not null"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// Transaction represents a financial transaction
type Transaction struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	UserID      uint           `json:"user_id" gorm:"not null"`
	Description string         `json:"description" gorm:"not null"`
	Amount      float64        `json:"amount" gorm:"not null"`
	Category    string         `json:"category" gorm:"not null"`
	Type        string         `json:"type" gorm:"not null"` // "income" or "expense"
	Currency    string         `json:"currency" gorm:"not null;default:'USD'"`
	Date        time.Time      `json:"date" gorm:"not null"`
	Notes       *string        `json:"notes"`
	IsRecurring bool           `json:"is_recurring" gorm:"default:false"`
	RecurringID *uint          `json:"recurring_id"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	User User `json:"user,omitempty"`
}

// RecurringTransaction represents a recurring financial transaction
type RecurringTransaction struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	UserID      uint           `json:"user_id" gorm:"not null"`
	Description string         `json:"description" gorm:"not null"`
	Amount      float64        `json:"amount" gorm:"not null"`
	Category    string         `json:"category" gorm:"not null"`
	Type        string         `json:"type" gorm:"not null"`      // "income" or "expense"
	Frequency   string         `json:"frequency" gorm:"not null"` // "weekly", "monthly", "yearly"
	StartDate   time.Time      `json:"start_date" gorm:"not null"`
	EndDate     *time.Time     `json:"end_date"`
	Notes       *string        `json:"notes"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	User User `json:"user,omitempty"`
}

// Invoice represents an invoice
// Status values: 'temporary' (not saved by user), 'draft' (user saved as draft), 'unpaid', 'paid', 'overdue', etc.
type Invoice struct {
	ID            uint           `json:"id" gorm:"primaryKey"`
	UserID        uint           `json:"user_id" gorm:"not null"`
	InvoiceNumber string         `json:"invoice_number" gorm:"uniqueIndex;not null"`
	Date          time.Time      `json:"date" gorm:"not null"`
	DueDate       time.Time      `json:"due_date" gorm:"not null"`
	Status        string         `json:"status" gorm:"default:'draft'"` // 'temporary', 'draft', 'unpaid', 'paid', 'overdue', etc.
	TaxRate       float64        `json:"tax_rate" gorm:"default:0"`
	Currency      string         `json:"currency" gorm:"not null;default:'USD'"`
	Notes         *string        `json:"notes"`
	CreatedAt     time.Time      `json:"created_at"`
	UpdatedAt     time.Time      `json:"updated_at"`
	DeletedAt     gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	User   User          `json:"user,omitempty"`
	Client InvoiceClient `json:"client" gorm:"foreignKey:InvoiceID"`
	Items  []InvoiceItem `json:"items" gorm:"foreignKey:InvoiceID"`
}

// InvoiceClient represents the client information for an invoice
type InvoiceClient struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	InvoiceID uint           `json:"invoice_id" gorm:"not null"`
	Name      string         `json:"name" gorm:"not null"`
	Email     string         `json:"email" gorm:"not null"`
	Address   *string        `json:"address"`
	Phone     *string        `json:"phone"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// InvoiceItem represents an item in an invoice
type InvoiceItem struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	InvoiceID   uint           `json:"invoice_id" gorm:"not null"`
	Description string         `json:"description" gorm:"not null"`
	Quantity    int            `json:"quantity" gorm:"not null"`
	Price       float64        `json:"price" gorm:"not null"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
}

// InvoiceTemplate represents a reusable invoice template
type InvoiceTemplate struct {
	ID             uint           `json:"id" gorm:"primaryKey"`
	UserID         uint           `json:"user_id" gorm:"not null"`
	Name           string         `json:"name" gorm:"not null"`
	DefaultTaxRate float64        `json:"default_tax_rate" gorm:"default:0"`
	Notes          *string        `json:"notes"`
	CreatedAt      time.Time      `json:"created_at"`
	UpdatedAt      time.Time      `json:"updated_at"`
	DeletedAt      gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	User  User                  `json:"user,omitempty"`
	Items []InvoiceTemplateItem `json:"items" gorm:"foreignKey:TemplateID"`
}

// InvoiceTemplateItem represents an item in an invoice template
type InvoiceTemplateItem struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	TemplateID  uint           `json:"template_id" gorm:"not null"`
	Description string         `json:"description" gorm:"not null"`
	Quantity    int            `json:"quantity" gorm:"not null"`
	Price       float64        `json:"price" gorm:"not null"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
}

// Budget represents a budget for a user (overall or per-category, monthly or yearly)
type Budget struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	UserID      uint           `json:"user_id" gorm:"not null;index"`
	Category    *string        `json:"category" gorm:"index"`        // null for overall budget
	PeriodType  string         `json:"period_type" gorm:"not null"`  // "monthly" or "yearly"
	PeriodStart time.Time      `json:"period_start" gorm:"not null"` // first day of the period
	Amount      float64        `json:"amount" gorm:"not null"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	User User `json:"user,omitempty"`
}

// CreateBudgetRequest for creating a budget
type CreateBudgetRequest struct {
	Category    *string   `json:"category"`
	PeriodType  string    `json:"period_type" binding:"required,oneof=monthly yearly"`
	PeriodStart time.Time `json:"period_start" binding:"required"`
	Amount      float64   `json:"amount" binding:"required"`
}

// UpdateBudgetRequest for updating a budget
type UpdateBudgetRequest struct {
	Amount float64 `json:"amount" binding:"required"`
}

// BudgetStatusResponse for budget vs. actuals and alerting
type BudgetStatusResponse struct {
	BudgetID    uint      `json:"budget_id"`
	Category    *string   `json:"category"`
	PeriodType  string    `json:"period_type"`
	PeriodStart time.Time `json:"period_start"`
	Amount      float64   `json:"amount"`
	Actual      float64   `json:"actual"`
	PercentUsed float64   `json:"percent_used"`
	AlertLevel  string    `json:"alert_level"` // "ok", "approaching", "over"
}

// Request/Response models

type LoginRequest struct {
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=6"`
}

type RegisterRequest struct {
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=6"`
	Name     string `json:"name" binding:"required"`
}

type AuthResponse struct {
	Token        string `json:"token"`
	RefreshToken string `json:"refresh_token"`
	User         User   `json:"user"`
}

type CreateTransactionRequest struct {
	Description string    `json:"description" binding:"required"`
	Amount      float64   `json:"amount" binding:"required"`
	Category    string    `json:"category" binding:"required"`
	Type        string    `json:"type" binding:"required,oneof=income expense"`
	Date        time.Time `json:"date" binding:"required"`
	Notes       *string   `json:"notes"`
}

type CreateRecurringTransactionRequest struct {
	Description string     `json:"description" binding:"required"`
	Amount      float64    `json:"amount" binding:"required"`
	Category    string     `json:"category" binding:"required"`
	Type        string     `json:"type" binding:"required,oneof=income expense"`
	Frequency   string     `json:"frequency" binding:"required,oneof=weekly monthly yearly"`
	StartDate   time.Time  `json:"start_date" binding:"required"`
	EndDate     *time.Time `json:"end_date"`
	Notes       *string    `json:"notes"`
}

type CreateInvoiceRequest struct {
	InvoiceNumber string              `json:"invoice_number" binding:"required"`
	Date          time.Time           `json:"date" binding:"required"`
	DueDate       time.Time           `json:"due_date" binding:"required"`
	TaxRate       float64             `json:"tax_rate"`
	Notes         *string             `json:"notes"`
	Client        CreateClientRequest `json:"client" binding:"required"`
	Items         []CreateItemRequest `json:"items" binding:"required,min=1"`
}

type CreateClientRequest struct {
	Name    string  `json:"name" binding:"required"`
	Email   string  `json:"email" binding:"required,email"`
	Address *string `json:"address"`
	Phone   *string `json:"phone"`
}

type CreateItemRequest struct {
	Description string  `json:"description" binding:"required"`
	Quantity    int     `json:"quantity" binding:"required,min=1"`
	Price       float64 `json:"price" binding:"required,min=0"`
}

type CreateInvoiceTemplateRequest struct {
	Name           string              `json:"name" binding:"required"`
	DefaultTaxRate float64             `json:"default_tax_rate"`
	Notes          *string             `json:"notes"`
	Items          []CreateItemRequest `json:"items" binding:"required,min=1"`
}

type SummaryResponse struct {
	TotalIncome       float64            `json:"total_income"`
	TotalExpenses     float64            `json:"total_expenses"`
	TotalBalance      float64            `json:"total_balance"`
	TotalInvoiced     float64            `json:"total_invoiced"`
	TotalPaid         float64            `json:"total_paid"`
	TotalOutstanding  float64            `json:"total_outstanding"`
	CategoryBreakdown map[string]float64 `json:"category_breakdown"`
}

type MonthlyTrendsResponse struct {
	Month    string  `json:"month"`
	Income   float64 `json:"income"`
	Expenses float64 `json:"expenses"`
	Balance  float64 `json:"balance"`
}
