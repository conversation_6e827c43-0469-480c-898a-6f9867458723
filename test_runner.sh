#!/bin/bash

# SecureBooks Test Runner Script
# This script runs all tests for the SecureBooks application

set -e  # Exit on any error

echo "🧪 SecureBooks Test Suite Runner"
echo "================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to run Flutter tests
run_flutter_tests() {
    print_status "Running Flutter tests..."
    
    # Install dependencies
    print_status "Installing Flutter dependencies..."
    flutter pub get
    
    # Generate mocks
    print_status "Generating mocks..."
    flutter packages pub run build_runner build --delete-conflicting-outputs
    
    # Run unit tests
    print_status "Running Flutter unit tests..."
    flutter test --coverage
    
    # Run widget tests
    print_status "Running Flutter widget tests..."
    flutter test test/widget_test.dart
    
    # Run model tests
    print_status "Running model tests..."
    flutter test test/models/
    
    # Run provider tests
    print_status "Running provider tests..."
    flutter test test/providers/
    
    print_success "Flutter tests completed!"
}

# Function to run Go backend tests
run_go_tests() {
    print_status "Running Go backend tests..."
    
    cd backend
    
    # Install test dependencies
    print_status "Installing Go test dependencies..."
    go mod tidy
    
    # Run tests with coverage
    print_status "Running Go unit tests..."
    go test -v -cover ./...
    
    # Run tests with race detection
    print_status "Running Go tests with race detection..."
    go test -race ./...
    
    # Generate coverage report
    print_status "Generating Go coverage report..."
    go test -coverprofile=coverage.out ./...
    go tool cover -html=coverage.out -o coverage.html
    
    cd ..
    
    print_success "Go backend tests completed!"
}

# Function to run integration tests
run_integration_tests() {
    print_status "Running integration tests..."
    
    # Check if device/emulator is available
    if ! flutter devices | grep -q "device"; then
        print_warning "No devices available for integration tests. Skipping..."
        return 0
    fi
    
    # Run integration tests
    flutter test integration_test/
    
    print_success "Integration tests completed!"
}

# Function to run linting and analysis
run_analysis() {
    print_status "Running code analysis..."
    
    # Flutter analysis
    print_status "Running Flutter analysis..."
    flutter analyze
    
    # Go linting (if golangci-lint is available)
    if command -v golangci-lint &> /dev/null; then
        print_status "Running Go linting..."
        cd backend
        golangci-lint run
        cd ..
    else
        print_warning "golangci-lint not found. Skipping Go linting."
    fi
    
    print_success "Code analysis completed!"
}

# Function to generate test reports
generate_reports() {
    print_status "Generating test reports..."
    
    # Create reports directory
    mkdir -p test_reports
    
    # Move coverage files
    if [ -f "coverage/lcov.info" ]; then
        cp coverage/lcov.info test_reports/flutter_coverage.lcov
    fi
    
    if [ -f "backend/coverage.html" ]; then
        cp backend/coverage.html test_reports/go_coverage.html
    fi
    
    print_success "Test reports generated in test_reports/ directory"
}

# Main execution
main() {
    local run_flutter=true
    local run_go=true
    local run_integration=false
    local run_lint=true
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --flutter-only)
                run_go=false
                run_integration=false
                shift
                ;;
            --go-only)
                run_flutter=false
                run_integration=false
                shift
                ;;
            --integration)
                run_integration=true
                shift
                ;;
            --no-lint)
                run_lint=false
                shift
                ;;
            --help)
                echo "Usage: $0 [OPTIONS]"
                echo "Options:"
                echo "  --flutter-only    Run only Flutter tests"
                echo "  --go-only        Run only Go backend tests"
                echo "  --integration    Include integration tests"
                echo "  --no-lint        Skip linting and analysis"
                echo "  --help           Show this help message"
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    # Run tests based on flags
    if [ "$run_flutter" = true ]; then
        run_flutter_tests
    fi
    
    if [ "$run_go" = true ]; then
        run_go_tests
    fi
    
    if [ "$run_integration" = true ]; then
        run_integration_tests
    fi
    
    if [ "$run_lint" = true ]; then
        run_analysis
    fi
    
    generate_reports
    
    print_success "All tests completed successfully! 🎉"
    echo ""
    echo "📊 Test Reports:"
    echo "  - Flutter coverage: test_reports/flutter_coverage.lcov"
    echo "  - Go coverage: test_reports/go_coverage.html"
    echo ""
    echo "🚀 Your application is ready for deployment!"
}

# Run main function with all arguments
main "$@"
