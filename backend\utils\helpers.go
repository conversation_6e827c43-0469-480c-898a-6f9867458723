package utils

import (
	"crypto/rand"
	"encoding/base64"
	"os"
	"strconv"
)

// GenerateRefreshTokenString generates a secure random string for refresh tokens
func GenerateRefreshTokenString() (string, error) {
	b := make([]byte, 32)
	_, err := rand.Read(b)
	if err != nil {
		return "", err
	}
	return base64.URLEncoding.EncodeToString(b), nil
}

// GetDraftExpirationDays returns the number of days before a draft is considered abandoned.
func GetDraftExpirationDays() int {
	daysStr := os.Getenv("DRAFT_EXPIRATION_DAYS")
	days, err := strconv.Atoi(daysStr)
	if err != nil || days <= 0 {
		return 30 // default to 30 days
	}
	return days
}
