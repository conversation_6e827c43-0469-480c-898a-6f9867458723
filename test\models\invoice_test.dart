import 'package:flutter_test/flutter_test.dart';
import 'package:securebooks/models/invoice.dart';

void main() {
  group('Invoice Model Tests', () {
    test('should create an invoice with all required fields', () {
      final invoice = Invoice(
        id: 1,
        invoiceNumber: 'INV-001',
        clientName: 'Test Client',
        clientEmail: '<EMAIL>',
        date: DateTime(2024, 1, 1),
        dueDate: DateTime(2024, 1, 31),
        items: [],
        taxRate: 0.1,
        status: 'draft',
      );

      expect(invoice.id, equals(1));
      expect(invoice.invoiceNumber, equals('INV-001'));
      expect(invoice.clientName, equals('Test Client'));
      expect(invoice.clientEmail, equals('<EMAIL>'));
      expect(invoice.date, equals(DateTime(2024, 1, 1)));
      expect(invoice.dueDate, equals(DateTime(2024, 1, 31)));
      expect(invoice.taxRate, equals(0.1));
      expect(invoice.status, equals('draft'));
    });

    test('should calculate total correctly with tax', () {
      final items = [
        InvoiceItem(
          id: 1,
          description: 'Item 1',
          quantity: 2,
          rate: 50.0,
        ),
        InvoiceItem(
          id: 2,
          description: 'Item 2',
          quantity: 1,
          rate: 100.0,
        ),
      ];

      final invoice = Invoice(
        id: 1,
        invoiceNumber: 'INV-001',
        clientName: 'Test Client',
        clientEmail: '<EMAIL>',
        date: DateTime(2024, 1, 1),
        dueDate: DateTime(2024, 1, 31),
        items: items,
        taxRate: 0.1,
        status: 'draft',
      );

      // Subtotal: (2 * 50) + (1 * 100) = 200
      // Tax: 200 * 0.1 = 20
      // Total: 200 + 20 = 220
      expect(invoice.subtotal, equals(200.0));
      expect(invoice.taxAmount, equals(20.0));
      expect(invoice.total, equals(220.0));
    });

    test('should convert invoice to JSON correctly', () {
      final items = [
        InvoiceItem(
          id: 1,
          description: 'Item 1',
          quantity: 1,
          rate: 100.0,
        ),
      ];

      final invoice = Invoice(
        id: 1,
        invoiceNumber: 'INV-001',
        clientName: 'Test Client',
        clientEmail: '<EMAIL>',
        date: DateTime(2024, 1, 1),
        dueDate: DateTime(2024, 1, 31),
        items: items,
        taxRate: 0.1,
        status: 'draft',
      );

      final json = invoice.toJson();

      expect(json['id'], equals(1));
      expect(json['invoice_number'], equals('INV-001'));
      expect(json['client_name'], equals('Test Client'));
      expect(json['client_email'], equals('<EMAIL>'));
      expect(json['tax_rate'], equals(0.1));
      expect(json['status'], equals('draft'));
      expect(json['items'], isA<List>());
    });

    test('should create invoice from JSON correctly', () {
      final json = {
        'id': 1,
        'invoice_number': 'INV-001',
        'client_name': 'Test Client',
        'client_email': '<EMAIL>',
        'date': '2024-01-01T00:00:00.000Z',
        'due_date': '2024-01-31T00:00:00.000Z',
        'tax_rate': 0.1,
        'status': 'draft',
        'items': [
          {
            'id': 1,
            'description': 'Item 1',
            'quantity': 1,
            'rate': 100.0,
          }
        ],
      };

      final invoice = Invoice.fromJson(json);

      expect(invoice.id, equals(1));
      expect(invoice.invoiceNumber, equals('INV-001'));
      expect(invoice.clientName, equals('Test Client'));
      expect(invoice.clientEmail, equals('<EMAIL>'));
      expect(invoice.taxRate, equals(0.1));
      expect(invoice.status, equals('draft'));
      expect(invoice.items.length, equals(1));
    });

    test('should copy invoice with updated fields', () {
      final original = Invoice(
        id: 1,
        invoiceNumber: 'INV-001',
        clientName: 'Test Client',
        clientEmail: '<EMAIL>',
        date: DateTime(2024, 1, 1),
        dueDate: DateTime(2024, 1, 31),
        items: [],
        taxRate: 0.1,
        status: 'draft',
      );

      final copied = original.copyWith(
        status: 'sent',
        taxRate: 0.15,
      );

      expect(copied.id, equals(original.id));
      expect(copied.invoiceNumber, equals(original.invoiceNumber));
      expect(copied.status, equals('sent'));
      expect(copied.taxRate, equals(0.15));
    });
  });

  group('InvoiceItem Model Tests', () {
    test('should create invoice item with all fields', () {
      final item = InvoiceItem(
        id: 1,
        description: 'Test Item',
        quantity: 2,
        rate: 50.0,
      );

      expect(item.id, equals(1));
      expect(item.description, equals('Test Item'));
      expect(item.quantity, equals(2));
      expect(item.rate, equals(50.0));
      expect(item.total, equals(100.0));
    });

    test('should calculate total correctly', () {
      final item = InvoiceItem(
        id: 1,
        description: 'Test Item',
        quantity: 3,
        rate: 25.5,
      );

      expect(item.total, equals(76.5));
    });

    test('should convert to JSON correctly', () {
      final item = InvoiceItem(
        id: 1,
        description: 'Test Item',
        quantity: 2,
        rate: 50.0,
      );

      final json = item.toJson();

      expect(json['id'], equals(1));
      expect(json['description'], equals('Test Item'));
      expect(json['quantity'], equals(2));
      expect(json['rate'], equals(50.0));
    });
  });
}
