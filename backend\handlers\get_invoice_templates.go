package handlers

import (
	"database/sql"
	"net/http"

	"github.com/gin-gonic/gin"
)

func GetInvoiceTemplatesHandler(c *gin.Context) {
	userID := c.GetInt("userID")
	db := c.MustGet("db").(*sql.DB)
	rows, err := db.Query("SELECT id, name, default_tax_rate, default_items, notes, created_at FROM invoice_templates WHERE user_id = $1 ORDER BY created_at DESC", userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error"})
		return
	}
	defer rows.Close()
	templates := []gin.H{}
	for rows.Next() {
		var t struct {
			ID             int
			Name           string
			DefaultTaxRate float64
			DefaultItems   string
			Notes          string
			CreatedAt      string
		}
		err := rows.Scan(&t.ID, &t.Name, &t.DefaultTaxRate, &t.DefaultItems, &t.Notes, &t.CreatedAt)
		if err != nil {
			c.<PERSON>(http.StatusInternalServerError, gin.H{"error": "Database error"})
			return
		}
		templates = append(templates, gin.H{
			"id":               t.ID,
			"name":             t.Name,
			"default_tax_rate": t.DefaultTaxRate,
			"default_items":    t.DefaultItems,
			"notes":            t.Notes,
			"created_at":       t.CreatedAt,
		})
	}
	c.JSON(http.StatusOK, gin.H{"templates": templates})
}
