import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../models/transaction.dart';
import '../services/database_service.dart';
import '../services/api/transaction.dart';
import 'auth_provider.dart';
import '../services/notification_service.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

class ExpenseProvider with ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService();
  final TransactionApi apiService;
  ExpenseProvider({required this.apiService});
  
  List<Transaction> _transactions = [];
  List<RecurringTransaction> _recurringTransactions = [];
  bool _isLoading = false;
  String? _error;

  String _searchText = '';
  String? _selectedCategory;
  String? _selectedType; // 'income', 'expense', or null
  DateTimeRange? _dateRange;

  List<Transaction> get transactions => _transactions;
  List<RecurringTransaction> get recurringTransactions => _recurringTransactions;
  bool get isLoading => _isLoading;
  String? get error => _error;

  String get searchText => _searchText;
  String? get selectedCategory => _selectedCategory;
  String? get selectedType => _selectedType;
  DateTimeRange? get dateRange => _dateRange;

  set searchText(String value) {
    _searchText = value;
    notifyListeners();
  }
  set selectedCategory(String? value) {
    _selectedCategory = value;
    notifyListeners();
  }
  set selectedType(String? value) {
    _selectedType = value;
    notifyListeners();
  }
  set dateRange(DateTimeRange? value) {
    _dateRange = value;
    notifyListeners();
  }

  void clearFilters() {
    _searchText = '';
    _selectedCategory = null;
    _selectedType = null;
    _dateRange = null;
    notifyListeners();
  }

  List<Transaction> get filteredTransactions {
    return _transactions.where((t) {
      final matchesSearch = _searchText.isEmpty ||
        t.description.toLowerCase().contains(_searchText.toLowerCase()) ||
        t.category.toLowerCase().contains(_searchText.toLowerCase());
      final matchesCategory = _selectedCategory == null || t.category == _selectedCategory;
      final matchesType = _selectedType == null || t.type == _selectedType;
      final matchesDate = _dateRange == null ||
        (t.date.isAfter(_dateRange!.start.subtract(const Duration(days: 1))) &&
         t.date.isBefore(_dateRange!.end.add(const Duration(days: 1))));
      return matchesSearch && matchesCategory && matchesType && matchesDate;
    }).toList();
  }

  double get totalIncome {
    return _transactions
        .where((t) => t.type == 'income')
        .fold(0.0, (sum, t) => sum + t.amount);
  }

  double get totalExpenses {
    return _transactions
        .where((t) => t.type == 'expense')
        .fold(0.0, (sum, t) => sum + t.amount);
  }

  double get totalBalance => totalIncome - totalExpenses;

  Map<String, double> get categoryTotals {
    Map<String, double> totals = {};
    for (var transaction in _transactions.where((t) => t.type == 'expense')) {
      totals[transaction.category] = (totals[transaction.category] ?? 0) + transaction.amount;
    }
    return totals;
  }

  List<String> get categories => [
    'Food & Dining',
    'Transportation',
    'Shopping',
    'Entertainment',
    'Healthcare',
    'Utilities',
    'Rent/Mortgage',
    'Insurance',
    'Education',
    'Travel',
    'Business',
    'Other',
  ];

  List<String> get incomeCategories => [
    'Salary',
    'Freelance',
    'Investment',
    'Business',
    'Gift',
    'Other',
  ];

  static const String _baseUrl = 'http://localhost:8080/api/v1';

  Future<void> loadTransactions({AuthProvider? authProvider}) async {
    _setLoading(true);
    try {
      if (authProvider?.token != null) {
        apiService.setAuthToken(authProvider!.token!);
      }
      final data = await apiService.getTransactions();
      _transactions = data;
      _error = null;
    } catch (e) {
      _error = 'Failed to load transactions: $e';
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> isOnline() async {
    final connectivityResult = await Connectivity().checkConnectivity();
    return connectivityResult != ConnectivityResult.none;
  }

  Future<void> addTransaction(Transaction transaction, {AuthProvider? authProvider, BuildContext? context}) async {
    _setLoading(true);
    try {
      if (!await isOnline()) {
        _error = 'No internet connection';
        if (context != null) {
          NotificationService.showErrorNotification(
            context,
            'No internet connection',
            title: 'Offline',
          );
        }
        return;
      }
      if (authProvider?.token != null) {
        apiService.setAuthToken(authProvider!.token!);
      }
      final savedTransaction = await apiService.addTransaction(transaction);
      _transactions.add(savedTransaction);
      _error = null;
      notifyListeners();
      if (context != null) {
        NotificationService.showSuccessNotification(
          context,
          '${transaction.description} successfully created',
          title: 'Transaction Added',
        );
      }
    } catch (e) {
      _error = 'Failed to add transaction: $e';
      if (context != null) {
        NotificationService.showErrorNotification(
          context,
          'Failed to create transaction: ${e.toString().replaceAll('Exception: ', '')}',
          title: 'Error',
        );
      }
    } finally {
      _setLoading(false);
    }
  }

  Future<void> updateTransaction(Transaction transaction, {AuthProvider? authProvider, BuildContext? context}) async {
    _setLoading(true);
    try {
      if (!await isOnline()) {
        _error = 'No internet connection';
        if (context != null) {
          NotificationService.showErrorNotification(
            context,
            'No internet connection',
            title: 'Offline',
          );
        }
        return;
      }
      final token = authProvider?.token;
      final response = await http.put(
        Uri.parse('$_baseUrl/transactions/${transaction.id}'),
        headers: {
          'Content-Type': 'application/json',
          if (token != null) 'Authorization': 'Bearer $token',
        },
        body: jsonEncode(transaction.toJson()),
      );
      final data = jsonDecode(response.body);
      if (response.statusCode == 200) {
        final updatedTransaction = Transaction.fromJson(data);
        final index = _transactions.indexWhere((t) => t.id == transaction.id);
        if (index != -1) {
          _transactions[index] = updatedTransaction;
        }
        _error = null;
        notifyListeners();
        if (context != null) {
          NotificationService.showSuccessNotification(
            context,
            '${transaction.description} successfully updated',
            title: 'Transaction Updated',
          );
        }
      } else {
        _error = data['error'] ?? 'Failed to update transaction';
        if (context != null) {
          NotificationService.showErrorNotification(
            context,
            _error!,
            title: 'Update Failed',
          );
        }
      }
    } catch (e) {
      _error = 'Failed to update transaction: $e';
      if (context != null) {
        NotificationService.showErrorNotification(
          context,
          'Failed to update transaction: ${e.toString().replaceAll('Exception: ', '')}',
          title: 'Error',
        );
      }
    } finally {
      _setLoading(false);
    }
  }

  Future<void> deleteTransaction(String id, {AuthProvider? authProvider, BuildContext? context}) async {
    _setLoading(true);
    try {
      if (!await isOnline()) {
        _error = 'No internet connection';
        if (context != null) {
          NotificationService.showErrorNotification(
            context,
            'No internet connection',
            title: 'Offline',
          );
        }
        return;
      }
      final token = authProvider?.token;
      final response = await http.delete(
        Uri.parse('$_baseUrl/transactions/$id'),
        headers: {
          'Content-Type': 'application/json',
          if (token != null) 'Authorization': 'Bearer $token',
        },
      );
      final data = jsonDecode(response.body);
      if (response.statusCode == 200) {
        _transactions.removeWhere((t) => t.id == id);
        _error = null;
        notifyListeners();
        if (context != null) {
          NotificationService.showSuccessNotification(
            context,
            'Transaction successfully deleted',
            title: 'Transaction Deleted',
          );
        }
      } else {
        _error = data['error'] ?? 'Failed to delete transaction';
        if (context != null) {
          NotificationService.showErrorNotification(
            context,
            _error!,
            title: 'Delete Failed',
          );
        }
      }
    } catch (e) {
      _error = 'Failed to delete transaction: $e';
      if (context != null) {
        NotificationService.showErrorNotification(
          context,
          'Failed to delete transaction: ${e.toString().replaceAll('Exception: ', '')}',
          title: 'Error',
        );
      }
    } finally {
      _setLoading(false);
    }
  }

  Future<void> loadRecurringTransactions({AuthProvider? authProvider}) async {
    _setLoading(true);
    try {
      final token = authProvider?.token;
      final response = await http.get(
        Uri.parse('$_baseUrl/recurring/'),
        headers: {
          'Content-Type': 'application/json',
          if (token != null) 'Authorization': 'Bearer $token',
        },
      );
      final data = jsonDecode(response.body);
      if (response.statusCode == 200 && data is List) {
        _recurringTransactions = data.map<RecurringTransaction>((r) => RecurringTransaction.fromJson(r)).toList();
        _error = null;
      } else {
        _error = data['error'] ?? 'Failed to load recurring transactions';
      }
    } catch (e) {
      _error = 'Failed to load recurring transactions: $e';
    } finally {
      _setLoading(false);
    }
  }

  Future<void> addRecurringTransaction(RecurringTransaction recurring, {AuthProvider? authProvider, BuildContext? context}) async {
    _setLoading(true);
    try {
      final token = authProvider?.token;
      final response = await http.post(
        Uri.parse('$_baseUrl/recurring/'),
        headers: {
          'Content-Type': 'application/json',
          if (token != null) 'Authorization': 'Bearer $token',
        },
        body: jsonEncode(recurring.toJson()),
      );
      final data = jsonDecode(response.body);
      if (response.statusCode == 200) {
        final savedRecurring = RecurringTransaction.fromJson(data);
        _recurringTransactions.add(savedRecurring);
        _error = null;
        notifyListeners();
        
        // TODO: Re-enable notifications when fixed
        // if (context != null) {
        //   NotificationService.showSuccessNotification(
        //     context,
        //     '${recurring.description} recurring transaction created',
        //     title: 'Recurring Transaction Added',
        //   );
        // }
      } else {
        _error = data['error'] ?? 'Failed to add recurring transaction';
        // TODO: Re-enable notifications when fixed
        // if (context != null) {
        //   NotificationService.showErrorNotification(
        //     context,
        //     _error!,
        //     title: 'Add Failed',
        //   );
        // }
      }
    } catch (e) {
      _error = 'Failed to add recurring transaction: $e';
      // TODO: Re-enable notifications when fixed
      // if (context != null) {
      //   NotificationService.showErrorNotification(
      //     context,
      //     'Failed to add recurring transaction: ${e.toString().replaceAll('Exception: ', '')}',
      //     title: 'Error',
      //   );
      // }
    } finally {
      _setLoading(false);
    }
  }

  Future<void> updateRecurringTransaction(RecurringTransaction recurring, {AuthProvider? authProvider}) async {
    _setLoading(true);
    try {
      final token = authProvider?.token;
      final response = await http.put(
        Uri.parse('$_baseUrl/recurring/${recurring.id}'),
        headers: {
          'Content-Type': 'application/json',
          if (token != null) 'Authorization': 'Bearer $token',
        },
        body: jsonEncode(recurring.toJson()),
      );
      final data = jsonDecode(response.body);
      if (response.statusCode == 200) {
        final updatedRecurring = RecurringTransaction.fromJson(data);
        final index = _recurringTransactions.indexWhere((r) => r.id == recurring.id);
        if (index != -1) {
          _recurringTransactions[index] = updatedRecurring;
        }
        _error = null;
        notifyListeners();
      } else {
        _error = data['error'] ?? 'Failed to update recurring transaction';
      }
    } catch (e) {
      _error = 'Failed to update recurring transaction: $e';
    } finally {
      _setLoading(false);
    }
  }

  Future<void> deleteRecurringTransaction(String id, {AuthProvider? authProvider}) async {
    _setLoading(true);
    try {
      final token = authProvider?.token;
      final response = await http.delete(
        Uri.parse('$_baseUrl/recurring/$id'),
        headers: {
          'Content-Type': 'application/json',
          if (token != null) 'Authorization': 'Bearer $token',
        },
      );
      final data = jsonDecode(response.body);
      if (response.statusCode == 200) {
        _recurringTransactions.removeWhere((r) => r.id == id);
        _error = null;
        notifyListeners();
      } else {
        _error = data['error'] ?? 'Failed to delete recurring transaction';
      }
    } catch (e) {
      _error = 'Failed to delete recurring transaction: $e';
    } finally {
      _setLoading(false);
    }
  }

  Future<void> _generateRecurringTransactions() async {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 0);

    for (var recurring in _recurringTransactions) {
      if (recurring.endDate != null && recurring.endDate!.isBefore(startOfMonth)) {
        continue;
      }

      DateTime currentDate = recurring.startDate;
      while (currentDate.isBefore(endOfMonth)) {
        if (currentDate.isAfter(startOfMonth)) {
          final existingTransaction = _transactions.any((t) =>
              t.recurringId == recurring.id &&
              t.date.year == currentDate.year &&
              t.date.month == currentDate.month);

          if (!existingTransaction) {
            final transaction = Transaction(
              id: DateTime.now().millisecondsSinceEpoch.toString(),
              description: recurring.description,
              amount: recurring.amount,
              category: recurring.category,
              date: currentDate,
              type: recurring.type,
              notes: recurring.notes,
              isRecurring: true,
              recurringId: recurring.id,
            );
            await addTransaction(transaction);
          }
        }

        switch (recurring.frequency) {
          case 'weekly':
            currentDate = currentDate.add(const Duration(days: 7));
            break;
          case 'monthly':
            currentDate = DateTime(currentDate.year, currentDate.month + 1, currentDate.day);
            break;
          case 'yearly':
            currentDate = DateTime(currentDate.year + 1, currentDate.month, currentDate.day);
            break;
        }
      }
    }
  }

  Future<void> exportToCSV() async {
    try {
      final csvData = await _databaseService.exportTransactionsToCSV();
      // Handle CSV export (save to file, share, etc.)
    } catch (e) {
      _error = 'Failed to export CSV: $e';
      notifyListeners();
    }
  }

  Future<void> _syncToCloud() async {
    try {
      await apiService.syncTransactions(_transactions);
      await apiService.syncRecurringTransactions(_recurringTransactions);
    } catch (e) {
      // Handle sync errors gracefully
      debugPrint('Sync failed: $e');
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }
} 