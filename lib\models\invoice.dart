import 'dart:convert';

class Invoice {
  final String id;
  final String invoiceNumber;
  final DateTime date;
  final DateTime dueDate;
  final Client client;
  final List<InvoiceItem> items;
  final double taxRate;
  final String status; // 'draft', 'sent', 'paid', 'overdue'
  final String? notes;
  final String currency;

  Invoice({
    required this.id,
    required this.invoiceNumber,
    required this.date,
    required this.dueDate,
    required this.client,
    required this.items,
    this.taxRate = 0.0,
    this.status = 'draft',
    this.notes,
    this.currency = 'USD',
  });

  double get subtotal {
    return items.fold(0.0, (sum, item) => sum + item.total);
  }

  double get taxAmount {
    return subtotal * (taxRate / 100);
  }

  double get total {
    return subtotal + taxAmount;
  }

  factory Invoice.fromJson(Map<String, dynamic> json) {
    return Invoice(
      id: (json['id'] ?? '').toString(),
      invoiceNumber: (json['invoice_number'] ?? '').toString(),
      date: json['date'] != null ? DateTime.parse(json['date']) : DateTime.now(),
      dueDate: json['due_date'] != null ? DateTime.parse(json['due_date']) : DateTime.now().add(const Duration(days: 30)),
      client: Client.fromJson(
        json['client'] ??
        (json.containsKey('client_name') || json.containsKey('client_email')
          ? {
              'name': (json['client_name'] ?? '').toString(),
              'email': (json['client_email'] ?? '').toString(),
            }
          : {'name': '', 'email': ''}
        )
      ),
      items: (json['items'] is List ? (json['items'] as List).map((i) => InvoiceItem.fromJson(i)).toList() : []),
      taxRate: (json['tax_rate'] ?? 0).toDouble(),
      status: (json['status'] ?? 'draft').toString(),
      notes: json.containsKey('notes') && json['notes'] != null ? json['notes'].toString() : null,
      currency: (json['currency'] ?? 'USD').toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'invoice_number': invoiceNumber,
      'date': date.toIso8601String(),
      'due_date': dueDate.toIso8601String(),
      'client': client.toJson(),
      'items': items.map((i) => i.toJson()).toList(),
      'tax_rate': taxRate,
      'status': status,
      'notes': notes,
      'currency': currency,
    };
  }

  Invoice copyWith({
    String? id,
    String? invoiceNumber,
    DateTime? date,
    DateTime? dueDate,
    Client? client,
    List<InvoiceItem>? items,
    double? taxRate,
    String? status,
    String? notes,
    String? currency,
  }) {
    return Invoice(
      id: id ?? this.id,
      invoiceNumber: invoiceNumber ?? this.invoiceNumber,
      date: date ?? this.date,
      dueDate: dueDate ?? this.dueDate,
      client: client ?? this.client,
      items: items ?? this.items,
      taxRate: taxRate ?? this.taxRate,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      currency: currency ?? this.currency,
    );
  }
}

class Client {
  final String name;
  final String email;
  final String? address;
  final String? phone;

  Client({
    required this.name,
    required this.email,
    this.address,
    this.phone,
  });

  factory Client.fromJson(Map<String, dynamic> json) {
    return Client(
      name: (json['name'] ?? '').toString(),
      email: (json['email'] ?? '').toString(),
      address: json['address']?.toString(),
      phone: json['phone']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'email': email,
      'address': address,
      'phone': phone,
    };
  }
}

class InvoiceItem {
  final String description;
  final int quantity;
  final double price;

  InvoiceItem({
    required this.description,
    required this.quantity,
    required this.price,
  });

  double get total => quantity * price;

  factory InvoiceItem.fromJson(Map<String, dynamic> json) {
    return InvoiceItem(
      description: json['description'],
      quantity: json['quantity'],
      price: json['price'].toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'description': description,
      'quantity': quantity,
      'price': price,
    };
  }

  InvoiceItem copyWith({
    String? description,
    int? quantity,
    double? price,
  }) {
    return InvoiceItem(
      description: description ?? this.description,
      quantity: quantity ?? this.quantity,
      price: price ?? this.price,
    );
  }
}

class InvoiceTemplate {
  final String id;
  final String name;
  final List<InvoiceItem> defaultItems;
  final double defaultTaxRate;
  final String? notes;

  InvoiceTemplate({
    required this.id,
    required this.name,
    required this.defaultItems,
    this.defaultTaxRate = 0.0,
    this.notes,
  });

  factory InvoiceTemplate.fromJson(Map<String, dynamic> json) {
    List<InvoiceItem> items = [];
    if (json['default_items'] != null && json['default_items'] is String && json['default_items'].isNotEmpty) {
      try {
        final itemsJson = jsonDecode(json['default_items']);
        if (itemsJson is List) {
          items = itemsJson.map((item) => InvoiceItem.fromJson(item)).toList();
        }
      } catch (e) {
        // If parsing fails, use empty list
        items = [];
      }
    }
    
    return InvoiceTemplate(
      id: json['id'].toString(),
      name: json['name'],
      defaultItems: items,
      defaultTaxRate: (json['default_tax_rate'] ?? 0).toDouble(),
      notes: json.containsKey('notes') && json['notes'] != null ? json['notes'].toString() : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'default_items': jsonEncode(defaultItems.map((item) => item.toJson()).toList()),
      'default_tax_rate': defaultTaxRate,
      'notes': notes,
    };
  }
} 