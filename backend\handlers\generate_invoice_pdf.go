package handlers

import (
	"database/sql"
	"fmt"
	"net/http"
	"strconv"

	"securebooks/models"

	"github.com/gin-gonic/gin"
	"github.com/jung-kurt/gofpdf"
)

type GeneratePDFRequest struct {
	InvoiceID uint `json:"invoice_id" binding:"required"`
}

func GenerateInvoicePDFHandler(c *gin.Context) {
	var req GeneratePDFRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	db := c.MustGet("db").(*sql.DB)
	userID := c.MustGet("userID").(int)

	// Get invoice data
	var invoice models.Invoice
	var client models.InvoiceClient
	var items []models.InvoiceItem

	// Query invoice
	err := db.QueryRow(`
		SELECT id, invoice_number, date, due_date, status, tax_rate, notes, created_at, updated_at
		FROM invoices WHERE id = ? AND user_id = ?
	`, req.InvoiceID, userID).Scan(
		&invoice.ID, &invoice.InvoiceNumber, &invoice.Date, &invoice.DueDate,
		&invoice.Status, &invoice.TaxRate, &invoice.Notes, &invoice.CreatedAt, &invoice.UpdatedAt,
	)

	if err == sql.ErrNoRows {
		c.JSON(http.StatusNotFound, gin.H{"error": "Invoice not found"})
		return
	} else if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error: " + err.Error()})
		return
	}

	// Query client
	err = db.QueryRow(`
		SELECT id, name, email, address, phone, created_at, updated_at
		FROM invoice_clients WHERE invoice_id = ?
	`, req.InvoiceID).Scan(
		&client.ID, &client.Name, &client.Email, &client.Address, &client.Phone,
		&client.CreatedAt, &client.UpdatedAt,
	)

	if err != nil && err != sql.ErrNoRows {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error: " + err.Error()})
		return
	}

	// Query items
	rows, err := db.Query(`
		SELECT id, description, quantity, price, created_at, updated_at
		FROM invoice_items WHERE invoice_id = ? ORDER BY id
	`, req.InvoiceID)

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error: " + err.Error()})
		return
	}
	defer rows.Close()

	for rows.Next() {
		var item models.InvoiceItem
		err := rows.Scan(
			&item.ID, &item.Description, &item.Quantity, &item.Price,
			&item.CreatedAt, &item.UpdatedAt,
		)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error: " + err.Error()})
			return
		}
		items = append(items, item)
	}

	// Generate PDF
	pdf := gofpdf.New("P", "mm", "A4", "")
	pdf.AddPage()

	// Set font
	pdf.SetFont("Arial", "B", 16)

	// Header
	pdf.Cell(0, 10, "INVOICE")
	pdf.Ln(15)

	// Invoice details
	pdf.SetFont("Arial", "B", 12)
	pdf.Cell(0, 8, "Invoice Number: "+invoice.InvoiceNumber)
	pdf.Ln(8)
	pdf.Cell(0, 8, "Date: "+invoice.Date.Format("January 2, 2006"))
	pdf.Ln(8)
	pdf.Cell(0, 8, "Due Date: "+invoice.DueDate.Format("January 2, 2006"))
	pdf.Ln(8)
	pdf.Cell(0, 8, "Status: "+invoice.Status)
	pdf.Ln(15)

	// Client information
	pdf.SetFont("Arial", "B", 12)
	pdf.Cell(0, 8, "Bill To:")
	pdf.Ln(8)
	pdf.SetFont("Arial", "", 10)
	pdf.Cell(0, 6, client.Name)
	pdf.Ln(6)
	pdf.Cell(0, 6, client.Email)
	pdf.Ln(6)
	if client.Address != nil {
		pdf.Cell(0, 6, *client.Address)
		pdf.Ln(6)
	}
	if client.Phone != nil {
		pdf.Cell(0, 6, *client.Phone)
		pdf.Ln(6)
	}
	pdf.Ln(10)

	// Items table
	pdf.SetFont("Arial", "B", 10)
	pdf.Cell(80, 8, "Description")
	pdf.Cell(30, 8, "Quantity")
	pdf.Cell(40, 8, "Price")
	pdf.Cell(40, 8, "Total")
	pdf.Ln(8)

	pdf.SetFont("Arial", "", 10)
	var subtotal float64
	for _, item := range items {
		itemTotal := float64(item.Quantity) * item.Price
		subtotal += itemTotal

		pdf.Cell(80, 6, item.Description)
		pdf.Cell(30, 6, strconv.Itoa(item.Quantity))
		pdf.Cell(40, 6, fmt.Sprintf("$%.2f", item.Price))
		pdf.Cell(40, 6, fmt.Sprintf("$%.2f", itemTotal))
		pdf.Ln(6)
	}

	// Totals
	pdf.Ln(5)
	pdf.SetFont("Arial", "B", 10)
	pdf.Cell(150, 8, "Subtotal:")
	pdf.Cell(40, 8, fmt.Sprintf("$%.2f", subtotal))
	pdf.Ln(8)

	if invoice.TaxRate > 0 {
		taxAmount := subtotal * (invoice.TaxRate / 100)
		pdf.Cell(150, 8, fmt.Sprintf("Tax (%.1f%%):", invoice.TaxRate))
		pdf.Cell(40, 8, fmt.Sprintf("$%.2f", taxAmount))
		pdf.Ln(8)
		subtotal += taxAmount
	}

	pdf.SetFont("Arial", "B", 12)
	pdf.Cell(150, 10, "Total:")
	pdf.Cell(40, 10, fmt.Sprintf("$%.2f", subtotal))
	pdf.Ln(10)

	// Notes
	if invoice.Notes != nil && *invoice.Notes != "" {
		pdf.Ln(10)
		pdf.SetFont("Arial", "B", 10)
		pdf.Cell(0, 8, "Notes:")
		pdf.Ln(8)
		pdf.SetFont("Arial", "", 10)
		pdf.MultiCell(0, 6, *invoice.Notes, "", "", false)
	}

	// Set response headers
	c.Header("Content-Type", "application/pdf")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=invoice_%s.pdf", invoice.InvoiceNumber))

	// Output PDF
	err = pdf.Output(c.Writer)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate PDF: " + err.Error()})
		return
	}
}
