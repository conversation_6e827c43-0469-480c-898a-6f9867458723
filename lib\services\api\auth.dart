import 'dart:convert';
import 'package:http/http.dart' as http;

class AuthApi {
  static const String baseUrl = 'http://localhost:8080/api/v1';
  String? _authToken;
  void Function()? onUnauthorized;

  void setAuthToken(String token) {
    _authToken = token;
  }

  void setOnUnauthorized(void Function() callback) {
    onUnauthorized = callback;
  }

  Map<String, String> get _headers {
    final headers = {
      'Content-Type': 'application/json',
    };
    if (_authToken != null) {
      headers['Authorization'] = 'Bearer $_authToken';
    }
    return headers;
  }

  void _handleUnauthorized() {
    if (onUnauthorized != null) {
      onUnauthorized!();
    }
  }

  Future<Map<String, dynamic>> login(String email, String password) async {
    final response = await http.post(
      Uri.parse('$baseUrl/auth/login'),
      headers: _headers,
      body: json.encode({
        'email': email,
        'password': password,
      }),
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      _authToken = data['token'];
      return data;
    } else {
      throw Exception('Login failed:  ${response.body}');
    }
  }

  Future<Map<String, dynamic>> register(String email, String password, String name) async {
    final response = await http.post(
      Uri.parse('$baseUrl/auth/register'),
      headers: _headers,
      body: json.encode({
        'email': email,
        'password': password,
        'name': name,
      }),
    );

    if (response.statusCode == 200 || response.statusCode == 201) {
      final data = json.decode(response.body);
      _authToken = data['token'];
      return data;
    } else {
      throw Exception('Registration failed:  ${response.body}');
    }
  }

  Future<void> deleteAccount(String password) async {
    final response = await http.delete(
      Uri.parse('$baseUrl/auth/delete'),
      headers: _headers,
      body: json.encode({'password': password}),
    );
    if (response.statusCode == 200) {
      return;
    } else if (response.statusCode == 401) {
      _handleUnauthorized();
      throw Exception('Incorrect password');
    } else {
      throw Exception('Failed to delete account:  ${response.body}');
    }
  }

  Future<void> resetPassword(String email) async {
    final response = await http.post(
      Uri.parse('$baseUrl/auth/reset-password'),
      headers: _headers,
      body: json.encode({'email': email}),
    );
    if (response.statusCode != 200) {
      throw Exception('Failed to reset password: ${response.body}');
    }
  }

  Future<void> confirmPasswordReset(String token, String newPassword) async {
    final response = await http.post(
      Uri.parse('$baseUrl/auth/confirm-reset-password'),
      headers: _headers,
      body: json.encode({
        'token': token,
        'new_password': newPassword,
      }),
    );
    if (response.statusCode != 200) {
      throw Exception('Failed to reset password: ${response.body}');
    }
  }
} 