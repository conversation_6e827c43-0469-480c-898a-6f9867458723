package main

import (
	"database/sql"
	"fmt"
	"os"

	_ "github.com/lib/pq"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

// Global SQL database connection
var DB *sql.DB

// Global GORM database connection (if needed for ORM queries)
var GormDB *gorm.DB

// InitDB initializes both the standard SQL and GORM database connections
func InitDB() error {
	dbURL := os.Getenv("DATABASE_URL")
	if dbURL == "" {
		return fmt.Errorf("DATABASE_URL is not set")
	}

	// Connect using database/sql
	db, err := sql.Open("postgres", dbURL)
	if err != nil {
		return fmt.Errorf("failed to open database: %v", err)
	}
	if err := db.Ping(); err != nil {
		return fmt.Errorf("failed to ping database: %v", err)
	}
	DB = db

	// Connect using GORM
	gormDB, err := gorm.Open(postgres.Open(dbURL), &gorm.Config{})
	if err != nil {
		return fmt.Errorf("failed to open GORM database: %v", err)
	}
	GormDB = gormDB

	// GORM AutoMigrate is disabled in production. Enable only when adding new features or updating schema.

	// Ensure invoice_templates exists (manual creation)
	_, err = DB.Exec(`
		CREATE TABLE IF NOT EXISTS invoice_templates (
			id SERIAL PRIMARY KEY,
			user_id INTEGER NOT NULL,
			name VARCHAR(255) NOT NULL,
			default_tax_rate DECIMAL(5,2) DEFAULT 0,
			default_items TEXT,
			notes TEXT,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		return fmt.Errorf("failed to create invoice_templates table: %v", err)
	}

	fmt.Println("Database initialized successfully (users table left untouched)")
	return nil
}
