package handlers

import (
	"database/sql"
	"net/http"

	"github.com/gin-gonic/gin"
)

func DeleteTransactionHandler(c *gin.Context) {
	userID := c.GetInt("userID")
	id := c.Param("id")
	db := c.MustGet("db").(*sql.DB)
	res, err := db.Exec("DELETE FROM transactions WHERE id = $1 AND user_id = $2", id, userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete transaction"})
		return
	}
	n, _ := res.RowsAffected()
	if n == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Transaction not found"})
		return
	}
	c.JSO<PERSON>(http.StatusOK, gin.H{"message": "Transaction deleted"})
}
