package handlers

import (
	"database/sql"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

func SpendingPatternsHandler(c *gin.Context) {
	userID := c.GetInt("userID")
	db := c.MustGet("db").(*sql.DB)
	rows, err := db.Query(`SELECT category, SUM(amount) FROM transactions WHERE user_id = $1 AND type = 'expense' GROUP BY category`, userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"categories": []gin.H{}})
		return
	}
	defer rows.Close()
	var total float64
	categories := []struct {
		Name  string
		Total float64
	}{}
	for rows.Next() {
		var name string
		var sum float64
		if err := rows.Scan(&name, &sum); err == nil {
			categories = append(categories, struct {
				Name  string
				Total float64
			}{name, sum})
			total += sum
		}
	}
	result := []gin.H{}
	for _, cat := range categories {
		percent := 0.0
		if total > 0 {
			percent = (cat.Total / total) * 100
		}
		result = append(result, gin.H{"name": cat.Name, "percent": int(percent + 0.5)})
	}
	c.<PERSON>(http.StatusOK, gin.H{"categories": result})
}

func ForecastHandler(c *gin.Context) {
	userID := c.GetInt("userID")
	db := c.MustGet("db").(*sql.DB)
	// Projected balance: income - expenses for current month
	now := time.Now()
	monthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	var income, expenses float64
	db.QueryRow(`SELECT COALESCE(SUM(amount),0) FROM transactions WHERE user_id = $1 AND type = 'income' AND date >= $2`, userID, monthStart).Scan(&income)
	db.QueryRow(`SELECT COALESCE(SUM(amount),0) FROM transactions WHERE user_id = $1 AND type = 'expense' AND date >= $2`, userID, monthStart).Scan(&expenses)
	projectedBalance := income - expenses
	// Forecast by category
	rows, err := db.Query(`SELECT category, SUM(amount) FROM transactions WHERE user_id = $1 AND type = 'expense' AND date >= $2 GROUP BY category`, userID, monthStart)
	forecastByCategory := []gin.H{}
	if err == nil {
		defer rows.Close()
		for rows.Next() {
			var cat string
			var sum float64
			if err := rows.Scan(&cat, &sum); err == nil {
				forecastByCategory = append(forecastByCategory, gin.H{"category": cat, "amount": sum})
			}
		}
	}
	c.JSON(http.StatusOK, gin.H{
		"projected_balance":    projectedBalance,
		"forecast_by_category": forecastByCategory,
	})
}

func AnomaliesHandler(c *gin.Context) {
	userID := c.GetInt("userID")
	db := c.MustGet("db").(*sql.DB)
	// Find transactions > 2x average for their category in the last 3 months
	threeMonthsAgo := time.Now().AddDate(0, -3, 0)
	rows, err := db.Query(`SELECT category, AVG(amount) FROM transactions WHERE user_id = $1 AND type = 'expense' AND date >= $2 GROUP BY category`, userID, threeMonthsAgo)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{"anomalies": []gin.H{}})
		return
	}
	catAvg := map[string]float64{}
	for rows.Next() {
		var cat string
		var avg float64
		if err := rows.Scan(&cat, &avg); err == nil {
			catAvg[cat] = avg
		}
	}
	rows.Close()
	// Now find large transactions
	rows, err = db.Query(`SELECT description, amount, date, category FROM transactions WHERE user_id = $1 AND type = 'expense' AND date >= $2`, userID, threeMonthsAgo)
	anomalies := []gin.H{}
	if err == nil {
		defer rows.Close()
		for rows.Next() {
			var desc, cat string
			var amt float64
			var date time.Time
			if err := rows.Scan(&desc, &amt, &date, &cat); err == nil {
				if avg, ok := catAvg[cat]; ok && avg > 0 && amt > 2*avg {
					anomalies = append(anomalies, gin.H{"description": desc, "amount": amt, "date": date.Format("2006-01-02")})
				}
			}
		}
	}
	c.JSON(http.StatusOK, gin.H{"anomalies": anomalies})
}

func HealthScoreHandler(c *gin.Context) {
	userID := c.GetInt("userID")
	db := c.MustGet("db").(*sql.DB)
	var income, expenses float64
	db.QueryRow(`SELECT COALESCE(SUM(amount),0) FROM transactions WHERE user_id = $1 AND type = 'income'`, userID).Scan(&income)
	db.QueryRow(`SELECT COALESCE(SUM(amount),0) FROM transactions WHERE user_id = $1 AND type = 'expense'`, userID).Scan(&expenses)
	score := 0
	max := 100
	msg := ""
	if income > 0 {
		savingsRate := (income - expenses) / income
		if savingsRate >= 0.2 {
			score = 90
			msg = "Excellent savings rate!"
		} else if savingsRate >= 0.1 {
			score = 75
			msg = "Good savings rate."
		} else if savingsRate >= 0.05 {
			score = 60
			msg = "Fair savings rate. Try to save more."
		} else {
			score = 40
			msg = "Low savings rate. Review your expenses."
		}
	} else {
		score = 30
		msg = "No income data."
	}
	c.JSON(http.StatusOK, gin.H{"score": score, "max": max, "message": msg})
}

func SavingsHandler(c *gin.Context) {
	userID := c.GetInt("userID")
	db := c.MustGet("db").(*sql.DB)
	var income, expenses float64
	db.QueryRow(`SELECT COALESCE(SUM(amount),0) FROM transactions WHERE user_id = $1 AND type = 'income'`, userID).Scan(&income)
	db.QueryRow(`SELECT COALESCE(SUM(amount),0) FROM transactions WHERE user_id = $1 AND type = 'expense'`, userID).Scan(&expenses)
	goal := 10000.0 // TODO: Make this user-configurable
	current := income - expenses
	var growth float64
	// Calculate investment growth as percent change in income over last 2 months
	now := time.Now()
	lastMonth := now.AddDate(0, -1, 0)
	prevMonth := now.AddDate(0, -2, 0)
	var lastIncome, prevIncome float64
	db.QueryRow(`SELECT COALESCE(SUM(amount),0) FROM transactions WHERE user_id = $1 AND type = 'income' AND date >= $2 AND date < $3`, userID, lastMonth, now).Scan(&lastIncome)
	db.QueryRow(`SELECT COALESCE(SUM(amount),0) FROM transactions WHERE user_id = $1 AND type = 'income' AND date >= $2 AND date < $3`, userID, prevMonth, lastMonth).Scan(&prevIncome)
	if prevIncome > 0 {
		growth = (lastIncome - prevIncome) / prevIncome
	} else {
		growth = 0
	}
	c.JSON(http.StatusOK, gin.H{
		"goal":              goal,
		"current":           current,
		"investment_growth": growth,
	})
}
