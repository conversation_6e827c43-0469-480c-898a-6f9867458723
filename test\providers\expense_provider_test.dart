import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:securebooks/providers/expense_provider.dart';
import 'package:securebooks/services/api/transaction.dart';
import 'package:securebooks/models/transaction.dart';

// Generate mocks
@GenerateMocks([TransactionApi])
import 'expense_provider_test.mocks.dart';

void main() {
  group('ExpenseProvider Tests', () {
    late ExpenseProvider provider;
    late MockTransactionApi mockApi;

    setUp(() {
      mockApi = MockTransactionApi();
      provider = ExpenseProvider(apiService: mockApi);
    });

    test('should initialize with empty transactions', () {
      expect(provider.transactions, isEmpty);
      expect(provider.isLoading, isFalse);
      expect(provider.error, isNull);
    });

    test('should load transactions successfully', () async {
      // Arrange
      final mockTransactions = [
        Transaction(
          id: 1,
          description: 'Test transaction',
          amount: 100.0,
          category: 'Food',
          date: DateTime(2024, 1, 1),
          type: TransactionType.expense,
        ),
      ];

      when(mockApi.getTransactions()).thenAnswer((_) async => mockTransactions);

      // Act
      await provider.loadTransactions();

      // Assert
      expect(provider.transactions, equals(mockTransactions));
      expect(provider.isLoading, isFalse);
      expect(provider.error, isNull);
      verify(mockApi.getTransactions()).called(1);
    });

    test('should handle load transactions error', () async {
      // Arrange
      when(mockApi.getTransactions()).thenThrow(Exception('Network error'));

      // Act
      await provider.loadTransactions();

      // Assert
      expect(provider.transactions, isEmpty);
      expect(provider.isLoading, isFalse);
      expect(provider.error, contains('Failed to load transactions'));
      verify(mockApi.getTransactions()).called(1);
    });

    test('should add transaction successfully', () async {
      // Arrange
      final newTransaction = Transaction(
        id: 0,
        description: 'New transaction',
        amount: 50.0,
        category: 'Transport',
        date: DateTime(2024, 1, 2),
        type: TransactionType.expense,
      );

      final savedTransaction = newTransaction.copyWith(id: 1);

      when(mockApi.addTransaction(any)).thenAnswer((_) async => savedTransaction);

      // Act
      await provider.addTransaction(newTransaction);

      // Assert
      expect(provider.transactions, contains(savedTransaction));
      expect(provider.error, isNull);
      verify(mockApi.addTransaction(newTransaction)).called(1);
    });

    test('should handle add transaction error', () async {
      // Arrange
      final newTransaction = Transaction(
        id: 0,
        description: 'New transaction',
        amount: 50.0,
        category: 'Transport',
        date: DateTime(2024, 1, 2),
        type: TransactionType.expense,
      );

      when(mockApi.addTransaction(any)).thenThrow(Exception('Server error'));

      // Act
      await provider.addTransaction(newTransaction);

      // Assert
      expect(provider.transactions, isEmpty);
      expect(provider.error, isNotNull);
      verify(mockApi.addTransaction(newTransaction)).called(1);
    });

    test('should update transaction successfully', () async {
      // Arrange
      final originalTransaction = Transaction(
        id: 1,
        description: 'Original',
        amount: 100.0,
        category: 'Food',
        date: DateTime(2024, 1, 1),
        type: TransactionType.expense,
      );

      final updatedTransaction = originalTransaction.copyWith(
        description: 'Updated',
        amount: 150.0,
      );

      provider.transactions.add(originalTransaction);

      when(mockApi.updateTransaction(any)).thenAnswer((_) async => updatedTransaction);

      // Act
      await provider.updateTransaction(updatedTransaction);

      // Assert
      expect(provider.transactions.first, equals(updatedTransaction));
      expect(provider.error, isNull);
      verify(mockApi.updateTransaction(updatedTransaction)).called(1);
    });

    test('should delete transaction successfully', () async {
      // Arrange
      final transaction = Transaction(
        id: 1,
        description: 'To delete',
        amount: 100.0,
        category: 'Food',
        date: DateTime(2024, 1, 1),
        type: TransactionType.expense,
      );

      provider.transactions.add(transaction);

      when(mockApi.deleteTransaction(1)).thenAnswer((_) async {});

      // Act
      await provider.deleteTransaction(1);

      // Assert
      expect(provider.transactions, isEmpty);
      expect(provider.error, isNull);
      verify(mockApi.deleteTransaction(1)).called(1);
    });

    test('should filter transactions by search text', () {
      // Arrange
      final transactions = [
        Transaction(
          id: 1,
          description: 'Coffee shop',
          amount: 5.0,
          category: 'Food',
          date: DateTime(2024, 1, 1),
          type: TransactionType.expense,
        ),
        Transaction(
          id: 2,
          description: 'Gas station',
          amount: 50.0,
          category: 'Transport',
          date: DateTime(2024, 1, 2),
          type: TransactionType.expense,
        ),
      ];

      provider.transactions.addAll(transactions);

      // Act
      provider.setSearchText('coffee');

      // Assert
      expect(provider.filteredTransactions.length, equals(1));
      expect(provider.filteredTransactions.first.description, contains('Coffee'));
    });

    test('should filter transactions by category', () {
      // Arrange
      final transactions = [
        Transaction(
          id: 1,
          description: 'Coffee',
          amount: 5.0,
          category: 'Food',
          date: DateTime(2024, 1, 1),
          type: TransactionType.expense,
        ),
        Transaction(
          id: 2,
          description: 'Gas',
          amount: 50.0,
          category: 'Transport',
          date: DateTime(2024, 1, 2),
          type: TransactionType.expense,
        ),
      ];

      provider.transactions.addAll(transactions);

      // Act
      provider.setSelectedCategory('Food');

      // Assert
      expect(provider.filteredTransactions.length, equals(1));
      expect(provider.filteredTransactions.first.category, equals('Food'));
    });

    test('should clear error', () {
      // Arrange
      provider.error = 'Some error';

      // Act
      provider.clearError();

      // Assert
      expect(provider.error, isNull);
    });
  });
}
