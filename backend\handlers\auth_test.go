package handlers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestRegisterHandler(t *testing.T) {
	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	t.Run("should register user with valid data", func(t *testing.T) {
		// Create a new Gin router
		router := gin.New()
		router.POST("/register", RegisterHandler)

		// Create request body
		requestBody := map[string]string{
			"email":    "<EMAIL>",
			"password": "password123",
		}
		jsonBody, _ := json.Marshal(requestBody)

		// Create request
		req, _ := http.NewRequest("POST", "/register", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		// Create response recorder
		w := httptest.NewRecorder()

		// Perform request
		router.ServeHTTP(w, req)

		// Note: This test will fail without a proper database setup
		// In a real test environment, you would mock the database
		// For now, we're just testing the handler structure
		assert.NotEqual(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("should reject invalid email format", func(t *testing.T) {
		router := gin.New()
		router.POST("/register", RegisterHandler)

		requestBody := map[string]string{
			"email":    "invalid-email",
			"password": "password123",
		}
		jsonBody, _ := json.Marshal(requestBody)

		req, _ := http.NewRequest("POST", "/register", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Should return bad request for invalid email
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("should reject empty password", func(t *testing.T) {
		router := gin.New()
		router.POST("/register", RegisterHandler)

		requestBody := map[string]string{
			"email":    "<EMAIL>",
			"password": "",
		}
		jsonBody, _ := json.Marshal(requestBody)

		req, _ := http.NewRequest("POST", "/register", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Should return bad request for empty password
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}

func TestLoginHandler(t *testing.T) {
	gin.SetMode(gin.TestMode)

	t.Run("should handle login request", func(t *testing.T) {
		router := gin.New()
		router.POST("/login", LoginHandler)

		requestBody := map[string]string{
			"email":    "<EMAIL>",
			"password": "password123",
		}
		jsonBody, _ := json.Marshal(requestBody)

		req, _ := http.NewRequest("POST", "/login", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Note: This test will fail without proper database setup
		// In a real test environment, you would mock the database
		assert.NotEqual(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("should reject invalid JSON", func(t *testing.T) {
		router := gin.New()
		router.POST("/login", LoginHandler)

		req, _ := http.NewRequest("POST", "/login", bytes.NewBuffer([]byte("invalid json")))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Should return bad request for invalid JSON
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}

func TestLogoutHandler(t *testing.T) {
	gin.SetMode(gin.TestMode)

	t.Run("should handle logout request", func(t *testing.T) {
		router := gin.New()
		router.POST("/logout", LogoutHandler)

		req, _ := http.NewRequest("POST", "/logout", nil)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		// Logout should return success
		assert.Equal(t, http.StatusOK, w.Code)
	})
}

func TestRefreshTokenHandler(t *testing.T) {
	gin.SetMode(gin.TestMode)

	t.Run("should handle refresh token request", func(t *testing.T) {
		router := gin.New()
		router.POST("/refresh", RefreshTokenHandler)

		requestBody := map[string]string{
			"refresh_token": "some-refresh-token",
		}
		jsonBody, _ := json.Marshal(requestBody)

		req, _ := http.NewRequest("POST", "/refresh", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Note: This test will fail without proper database setup
		// In a real test environment, you would mock the database
		assert.NotEqual(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("should reject empty refresh token", func(t *testing.T) {
		router := gin.New()
		router.POST("/refresh", RefreshTokenHandler)

		requestBody := map[string]string{
			"refresh_token": "",
		}
		jsonBody, _ := json.Marshal(requestBody)

		req, _ := http.NewRequest("POST", "/refresh", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Should return bad request for empty refresh token
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}

// Helper function to create a test context with user ID
func createTestContextWithUser(userID uint) *gin.Context {
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Set("userID", userID)
	return c
}

// Test helper to validate JSON response structure
func validateJSONResponse(t *testing.T, body []byte, expectedFields []string) {
	var response map[string]interface{}
	err := json.Unmarshal(body, &response)
	assert.NoError(t, err, "Response should be valid JSON")

	for _, field := range expectedFields {
		_, exists := response[field]
		assert.True(t, exists, "Response should contain field: %s", field)
	}
}
