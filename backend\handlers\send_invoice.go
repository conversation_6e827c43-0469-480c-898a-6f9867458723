package handlers

import (
	"database/sql"
	"fmt"
	"net/http"
	"os"
	"time"

	"securebooks/models"

	"github.com/gin-gonic/gin"
	"github.com/sendgrid/sendgrid-go"
	"github.com/sendgrid/sendgrid-go/helpers/mail"
)

type SendInvoiceRequest struct {
	InvoiceID uint `json:"invoice_id" binding:"required"`
}

func SendInvoiceHandler(c *gin.Context) {
	var req SendInvoiceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	db := c.MustGet("db").(*sql.DB)
	userID := c.MustGet("userID").(int)

	// Get invoice data
	var invoice models.Invoice
	var client models.InvoiceClient

	// Query invoice
	err := db.QueryRow(`
		SELECT id, invoice_number, date, due_date, status, tax_rate, notes, created_at, updated_at
		FROM invoices WHERE id = ? AND user_id = ?
	`, req.InvoiceID, userID).Scan(
		&invoice.ID, &invoice.InvoiceNumber, &invoice.Date, &invoice.DueDate,
		&invoice.Status, &invoice.TaxRate, &invoice.Notes, &invoice.CreatedAt, &invoice.UpdatedAt,
	)

	if err == sql.ErrNoRows {
		c.JSON(http.StatusNotFound, gin.H{"error": "Invoice not found"})
		return
	} else if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error: " + err.Error()})
		return
	}

	// Query client
	err = db.QueryRow(`
		SELECT id, name, email, address, phone, created_at, updated_at
		FROM invoice_clients WHERE invoice_id = ?
	`, req.InvoiceID).Scan(
		&client.ID, &client.Name, &client.Email, &client.Address, &client.Phone,
		&client.CreatedAt, &client.UpdatedAt,
	)

	if err != nil && err != sql.ErrNoRows {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error: " + err.Error()})
		return
	}

	// Send email
	sendgridAPIKey := os.Getenv("SENDGRID_API_KEY")
	fromEmail := os.Getenv("FROM_EMAIL")

	if sendgridAPIKey == "" || fromEmail == "" {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Email configuration not set"})
		return
	}

	// Create email content
	subject := fmt.Sprintf("Invoice #%s from SecureBooks", invoice.InvoiceNumber)

	// Generate email body
	body := fmt.Sprintf(`
Dear %s,

Please find attached invoice #%s for the amount of $%.2f.

Invoice Details:
- Invoice Number: %s
- Date: %s
- Due Date: %s
- Amount: $%.2f

Please contact us if you have any questions.

Best regards,
SecureBooks Team
	`, client.Name, invoice.InvoiceNumber, invoice.TaxRate, invoice.InvoiceNumber,
		invoice.Date.Format("January 2, 2006"), invoice.DueDate.Format("January 2, 2006"), invoice.TaxRate)

	// Create email
	from := mail.NewEmail("SecureBooks", fromEmail)
	to := mail.NewEmail(client.Name, client.Email)
	message := mail.NewSingleEmail(from, subject, to, body, body)

	// Send email
	sendgridClient := sendgrid.NewSendClient(sendgridAPIKey)
	response, err := sendgridClient.Send(message)

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to send email: " + err.Error()})
		return
	}

	if response.StatusCode >= 400 {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Email service error: " + response.Body})
		return
	}

	// Update invoice status to 'sent'
	_, err = db.Exec(`
		UPDATE invoices 
		SET status = ?, updated_at = ?
		WHERE id = ? AND user_id = ?
	`, "sent", time.Now(), req.InvoiceID, userID)

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update invoice status: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":    "Invoice sent successfully",
		"email_sent": true,
		"invoice_id": req.InvoiceID,
	})
}
