import 'package:flutter/material.dart';
import '../models/budget.dart';
import '../services/api/budget_api.dart';

class BudgetProvider with ChangeNotifier {
  final BudgetApi api;
  List<Budget> _budgets = [];
  List<BudgetStatus> _statuses = [];
  bool _isLoading = false;
  String? _error;

  BudgetProvider({required this.api});

  List<Budget> get budgets => _budgets;
  List<BudgetStatus> get statuses => _statuses;
  bool get isLoading => _isLoading;
  String? get error => _error;

  Future<void> fetchBudgets() async {
    _isLoading = true;
    notifyListeners();
    try {
      _budgets = await api.fetchBudgets();
      _error = null;
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> fetchBudgetStatus() async {
    _isLoading = true;
    notifyListeners();
    try {
      _statuses = await api.fetchBudgetStatus();
      _error = null;
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> loadBudgetsAndStatus() async {
    _isLoading = true;
    notifyListeners();
    try {
      await Future.wait([
        fetchBudgets(),
        fetchBudgetStatus(),
      ]);
      _error = null;
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> addBudget(Budget budget) async {
    _isLoading = true;
    notifyListeners();
    try {
      await api.addBudget(budget);
      await loadBudgetsAndStatus();
      _error = null;
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> updateBudget(Budget budget) async {
    _isLoading = true;
    notifyListeners();
    try {
      await api.updateBudget(budget);
      await loadBudgetsAndStatus();
      _error = null;
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> deleteBudget(int id) async {
    _isLoading = true;
    notifyListeners();
    try {
      await api.deleteBudget(id);
      await loadBudgetsAndStatus();
      _error = null;
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
} 