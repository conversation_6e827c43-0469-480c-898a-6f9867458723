package handlers

import (
	"database/sql"
	"net/http"

	"github.com/gin-gonic/gin"
)

func GetInvoiceTemplateHandler(c *gin.Context) {
	userID := c.GetInt("userID")
	id := c.<PERSON>("id")
	var t struct {
		ID             int
		Name           string
		DefaultTaxRate float64
		DefaultItems   string
		Notes          string
		CreatedAt      string
	}
	db := c.MustGet("db").(*sql.DB)
	err := db.QueryRow("SELECT id, name, default_tax_rate, default_items, notes, created_at FROM invoice_templates WHERE id = $1 AND user_id = $2", id, userID).Scan(&t.ID, &t.Name, &t.DefaultTaxRate, &t.DefaultItems, &t.Notes, &t.CreatedAt)
	if err == sql.ErrNoRows {
		c.JSON(http.StatusNotFound, gin.H{"error": "Template not found"})
		return
	} else if err != nil {
		c.<PERSON>(http.StatusInternalServerError, gin.H{"error": "Database error"})
		return
	}
	c.<PERSON>(http.StatusOK, gin.H{
		"id":               t.ID,
		"name":             t.Name,
		"default_tax_rate": t.DefaultTaxRate,
		"default_items":    t.DefaultItems,
		"notes":            t.Notes,
		"created_at":       t.CreatedAt,
	})
}
