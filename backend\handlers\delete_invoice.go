package handlers

import (
	"database/sql"
	"net/http"

	"github.com/gin-gonic/gin"
)

func DeleteInvoiceHandler(c *gin.Context) {
	userID := c.GetInt("userID")
	id := c.Param("id")
	db := c.MustGet("db").(*sql.DB)
	res, err := db.Exec("DELETE FROM invoices WHERE id = $1 AND user_id = $2", id, userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete invoice"})
		return
	}
	n, _ := res.RowsAffected()
	if n == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Invoice not found"})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Invoice deleted"})
}
