package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"securebooks/services"
)

type MonitoringHandlers struct {
	monitoringService *services.MonitoringService
}

func NewMonitoringHandlers(monitoringService *services.MonitoringService) *MonitoringHandlers {
	return &MonitoringHandlers{
		monitoringService: monitoringService,
	}
}

// MetricsHandler exposes Prometheus metrics
func (mh *MonitoringHandlers) MetricsHandler() gin.HandlerFunc {
	handler := promhttp.Handler()
	return func(c *gin.Context) {
		handler.ServeHTTP(c.Writer, c.Request)
	}
}

// SystemMetricsHandler returns current system metrics
func (mh *MonitoringHandlers) SystemMetricsHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		metrics := mh.monitoringService.GetMetrics()
		c.<PERSON>(http.StatusOK, gin.H{
			"status": "success",
			"data":   metrics,
		})
	}
}

// AlertsHandler returns current alerts
func (mh *MonitoringHandlers) AlertsHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		alerts := mh.monitoringService.GetAlerts()
		
		// Filter by severity if requested
		severity := c.Query("severity")
		if severity != "" {
			filteredAlerts := make([]services.Alert, 0)
			for _, alert := range alerts {
				if alert.Severity == severity {
					filteredAlerts = append(filteredAlerts, alert)
				}
			}
			alerts = filteredAlerts
		}

		// Filter by resolved status if requested
		resolvedParam := c.Query("resolved")
		if resolvedParam != "" {
			resolved, err := strconv.ParseBool(resolvedParam)
			if err == nil {
				filteredAlerts := make([]services.Alert, 0)
				for _, alert := range alerts {
					if alert.Resolved == resolved {
						filteredAlerts = append(filteredAlerts, alert)
					}
				}
				alerts = filteredAlerts
			}
		}

		c.JSON(http.StatusOK, gin.H{
			"status": "success",
			"data":   alerts,
			"count":  len(alerts),
		})
	}
}

// ResolveAlertHandler resolves a specific alert
func (mh *MonitoringHandlers) ResolveAlertHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		alertID := c.Param("id")
		if alertID == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"status": "error",
				"message": "Alert ID is required",
			})
			return
		}

		err := mh.monitoringService.ResolveAlert(alertID)
		if err != nil {
			c.JSON(http.StatusNotFound, gin.H{
				"status": "error",
				"message": err.Error(),
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"status": "success",
			"message": "Alert resolved successfully",
		})
	}
}

// DashboardHandler returns a comprehensive monitoring dashboard data
func (mh *MonitoringHandlers) DashboardHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		metrics := mh.monitoringService.GetMetrics()
		alerts := mh.monitoringService.GetAlerts()

		// Count alerts by severity
		alertCounts := map[string]int{
			"critical": 0,
			"warning":  0,
			"info":     0,
		}

		activeAlerts := 0
		for _, alert := range alerts {
			if !alert.Resolved {
				activeAlerts++
				alertCounts[alert.Severity]++
			}
		}

		// Calculate system health score (0-100)
		healthScore := calculateHealthScore(metrics, activeAlerts)

		dashboard := gin.H{
			"system_metrics": metrics,
			"alerts": gin.H{
				"total":        len(alerts),
				"active":       activeAlerts,
				"by_severity":  alertCounts,
				"recent":       getRecentAlerts(alerts, 5),
			},
			"health_score": healthScore,
			"status":       getOverallStatus(healthScore),
		}

		c.JSON(http.StatusOK, gin.H{
			"status": "success",
			"data":   dashboard,
		})
	}
}

// calculateHealthScore calculates a health score based on metrics and alerts
func calculateHealthScore(metrics services.SystemMetrics, activeAlerts int) int {
	score := 100

	// Deduct points for high resource usage
	if metrics.CPUUsage > 80 {
		score -= 20
	} else if metrics.CPUUsage > 60 {
		score -= 10
	}

	if metrics.MemoryUsage > 85 {
		score -= 20
	} else if metrics.MemoryUsage > 70 {
		score -= 10
	}

	// Deduct points for high error rate
	if metrics.ErrorRate > 5 {
		score -= 25
	} else if metrics.ErrorRate > 2 {
		score -= 10
	}

	// Deduct points for active alerts
	score -= activeAlerts * 5

	// Ensure score doesn't go below 0
	if score < 0 {
		score = 0
	}

	return score
}

// getOverallStatus returns the overall system status based on health score
func getOverallStatus(healthScore int) string {
	if healthScore >= 90 {
		return "excellent"
	} else if healthScore >= 75 {
		return "good"
	} else if healthScore >= 50 {
		return "degraded"
	} else {
		return "critical"
	}
}

// getRecentAlerts returns the most recent alerts
func getRecentAlerts(alerts []services.Alert, limit int) []services.Alert {
	if len(alerts) <= limit {
		return alerts
	}

	// Sort alerts by timestamp (most recent first)
	// For simplicity, we'll just return the last 'limit' alerts
	// In production, you'd want to properly sort by timestamp
	return alerts[len(alerts)-limit:]
}

// StatusHandler returns a simple status endpoint for monitoring tools
func (mh *MonitoringHandlers) StatusHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		metrics := mh.monitoringService.GetMetrics()
		alerts := mh.monitoringService.GetAlerts()

		activeAlerts := 0
		criticalAlerts := 0
		for _, alert := range alerts {
			if !alert.Resolved {
				activeAlerts++
				if alert.Severity == "critical" {
					criticalAlerts++
				}
			}
		}

		status := "healthy"
		if criticalAlerts > 0 {
			status = "critical"
		} else if activeAlerts > 0 || metrics.ErrorRate > 5 {
			status = "degraded"
		}

		statusCode := http.StatusOK
		if status == "critical" {
			statusCode = http.StatusServiceUnavailable
		} else if status == "degraded" {
			statusCode = http.StatusPartialContent
		}

		c.JSON(statusCode, gin.H{
			"status":          status,
			"active_alerts":   activeAlerts,
			"critical_alerts": criticalAlerts,
			"error_rate":      metrics.ErrorRate,
			"uptime":          metrics.LastUpdated,
		})
	}
}
