import 'dart:convert';
import 'package:http/http.dart' as http;

class AnalyticsApi {
  static const String baseUrl = 'http://localhost:8080/api/v1';
  String? _authToken;
  void Function()? onUnauthorized;

  void setAuthToken(String token) {
    _authToken = token;
  }

  void setOnUnauthorized(void Function() callback) {
    onUnauthorized = callback;
  }

  Map<String, String> get _headers {
    final headers = {
      'Content-Type': 'application/json',
    };
    if (_authToken != null) {
      headers['Authorization'] = 'Bearer $_authToken';
    }
    return headers;
  }

  void _handleUnauthorized() {
    if (onUnauthorized != null) {
      onUnauthorized!();
    }
  }

  Future<Map<String, dynamic>> getAnalytics() async {
    final response = await http.get(
      Uri.parse('$baseUrl/analytics/summary'),
      headers: _headers,
    );

    if (response.statusCode == 401) {
      _handleUnauthorized();
      throw Exception('Unauthorized');
    }
    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('Failed to get analytics:  $response.body');
    }
  }
} 