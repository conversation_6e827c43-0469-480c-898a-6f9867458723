package handlers

import (
	"net/http"
	"time"

	"securebooks/models"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

func CreateBudgetHandler(c *gin.Context) {
	var req models.CreateBudgetRequest
	if err := c.ShouldBind<PERSON>(&req); err != nil {
		c.JSO<PERSON>(http.StatusBadRequest, gin.H{"error": "Invalid request: " + err.Error()})
		return
	}

	userIDVal, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}
	userID, ok := userIDVal.(int)
	if !ok {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user ID"})
		return
	}

	db, ok := c.MustGet("db").(*gorm.DB)
	if !ok {
		c.J<PERSON>(http.StatusInternalServerError, gin.H{"error": "Database connection error"})
		return
	}

	budget := models.Budget{
		UserID:      uint(userID),
		Category:    req.Category,
		PeriodType:  req.PeriodType,
		PeriodStart: req.PeriodStart,
		Amount:      req.Amount,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}
	if err := db.Create(&budget).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create budget: " + err.Error()})
		return
	}
	c.JSON(http.StatusCreated, budget)
}

func ListBudgetsHandler(c *gin.Context) {
	userIDVal, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}
	userID, ok := userIDVal.(int)
	if !ok {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user ID"})
		return
	}

	db, ok := c.MustGet("db").(*gorm.DB)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database connection error"})
		return
	}

	var budgets []models.Budget
	query := db.Where("user_id = ?", userID)

	if periodType := c.Query("period_type"); periodType != "" {
		query = query.Where("period_type = ?", periodType)
	}
	if periodStart := c.Query("period_start"); periodStart != "" {
		if t, err := time.Parse("2006-01-02", periodStart); err == nil {
			query = query.Where("period_start = ?", t)
		} else {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid period_start format, use YYYY-MM-DD"})
			return
		}
	}
	if category := c.Query("category"); category != "" {
		query = query.Where("category = ?", category)
	}

	if err := query.Find(&budgets).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch budgets: " + err.Error()})
		return
	}
	c.JSON(http.StatusOK, budgets)
}

func UpdateBudgetHandler(c *gin.Context) {
	userIDVal, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}
	userID, ok := userIDVal.(int)
	if !ok {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user ID"})
		return
	}

	db, ok := c.MustGet("db").(*gorm.DB)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database connection error"})
		return
	}

	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Missing budget id"})
		return
	}

	var req models.UpdateBudgetRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request: " + err.Error()})
		return
	}

	var budget models.Budget
	if err := db.Where("id = ? AND user_id = ?", id, userID).First(&budget).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Budget not found"})
		return
	}

	budget.Amount = req.Amount
	budget.UpdatedAt = time.Now()
	if err := db.Save(&budget).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update budget: " + err.Error()})
		return
	}
	c.JSON(http.StatusOK, budget)
}

func DeleteBudgetHandler(c *gin.Context) {
	userIDVal, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}
	userID, ok := userIDVal.(int)
	if !ok {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user ID"})
		return
	}

	db, ok := c.MustGet("db").(*gorm.DB)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database connection error"})
		return
	}

	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Missing budget id"})
		return
	}

	var budget models.Budget
	if err := db.Where("id = ? AND user_id = ?", id, userID).First(&budget).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Budget not found"})
		return
	}

	if err := db.Delete(&budget).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete budget: " + err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Budget deleted"})
}

func GetBudgetStatusHandler(c *gin.Context) {
	userIDVal, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}
	userID, ok := userIDVal.(int)
	if !ok {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user ID"})
		return
	}

	db, ok := c.MustGet("db").(*gorm.DB)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database connection error"})
		return
	}

	// Get all budgets for the user for the current period
	now := time.Now()
	var budgets []models.Budget
	if err := db.Where("user_id = ? AND deleted_at IS NULL", userID).Find(&budgets).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch budgets: " + err.Error()})
		return
	}

	dbSQL, _ := db.DB()
	statuses := []gin.H{}
	for _, budget := range budgets {
		var periodStart, periodEnd time.Time
		if budget.PeriodType == "monthly" {
			periodStart = time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
			periodEnd = periodStart.AddDate(0, 1, 0)
		} else if budget.PeriodType == "yearly" {
			periodStart = time.Date(now.Year(), 1, 1, 0, 0, 0, 0, now.Location())
			periodEnd = periodStart.AddDate(1, 0, 0)
		} else {
			continue
		}
		var actual float64
		if budget.Category == nil {
			// Overall budget: sum all expenses
			dbSQL.QueryRow(`SELECT COALESCE(SUM(amount),0) FROM transactions WHERE user_id = $1 AND type = 'expense' AND date >= $2 AND date < $3`, userID, periodStart, periodEnd).Scan(&actual)
		} else {
			dbSQL.QueryRow(`SELECT COALESCE(SUM(amount),0) FROM transactions WHERE user_id = $1 AND type = 'expense' AND category = $2 AND date >= $3 AND date < $4`, userID, *budget.Category, periodStart, periodEnd).Scan(&actual)
		}
		percentUsed := 0.0
		if budget.Amount > 0 {
			percentUsed = actual / budget.Amount
		}
		alert := "ok"
		if percentUsed >= 1.0 {
			alert = "over"
		} else if percentUsed >= 0.9 {
			alert = "approaching"
		}
		statuses = append(statuses, gin.H{
			"budget_id":    budget.ID,
			"category":     budget.Category,
			"period_type":  budget.PeriodType,
			"period_start": budget.PeriodStart,
			"amount":       budget.Amount,
			"actual":       actual,
			"percent_used": percentUsed,
			"alert_level":  alert,
		})
	}
	c.JSON(http.StatusOK, statuses)
}
