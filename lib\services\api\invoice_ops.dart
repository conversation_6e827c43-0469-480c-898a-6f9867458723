import 'package:http/http.dart' as http;
import '../../models/invoice.dart';

class InvoiceOpsApi {
  static const String baseUrl = 'http://localhost:8080/api/v1';
  String? _authToken;
  void Function()? onUnauthorized;

  void setAuthToken(String token) {
    _authToken = token;
  }

  void setOnUnauthorized(void Function() callback) {
    onUnauthorized = callback;
  }

  Map<String, String> get _headers {
    final headers = {
      'Content-Type': 'application/json',
    };
    if (_authToken != null) {
      headers['Authorization'] = 'Bearer $_authToken';
    }
    return headers;
  }

  void _handleUnauthorized() {
    if (onUnauthorized != null) {
      onUnauthorized!();
    }
  }

  Future<void> sendInvoice(Invoice invoice) async {
    final response = await http.post(
      Uri.parse('$baseUrl/invoices/${invoice.id}/send'),
      headers: _headers,
    );

    if (response.statusCode == 401) {
      _handleUnauthorized();
      throw Exception('Unauthorized');
    }
    if (response.statusCode != 200) {
      throw Exception('Failed to send invoice:  $response.body');
    }
  }
} 