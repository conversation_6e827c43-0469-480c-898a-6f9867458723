package models

import (
	"testing"
	"time"
)

func TestUser(t *testing.T) {
	t.Run("should create user with valid data", func(t *testing.T) {
		user := User{
			ID:       1,
			Email:    "<EMAIL>",
			Password: "hashedpassword",
		}

		if user.ID != 1 {
			t.<PERSON><PERSON><PERSON>("Expected ID to be 1, got %d", user.ID)
		}
		if user.Email != "<EMAIL>" {
			t.Errorf("Expected email <NAME_EMAIL>, got %s", user.Email)
		}
	})
}

func TestTransaction(t *testing.T) {
	t.Run("should create transaction with valid data", func(t *testing.T) {
		now := time.Now()
		transaction := Transaction{
			ID:          1,
			UserID:      1,
			Description: "Test transaction",
			Amount:      100.50,
			Category:    "Food",
			Type:        "expense",
			Date:        now,
		}

		if transaction.ID != 1 {
			t.Errorf("Expected ID to be 1, got %d", transaction.ID)
		}
		if transaction.UserID != 1 {
			t.<PERSON><PERSON><PERSON>("Expected UserID to be 1, got %d", transaction.UserID)
		}
		if transaction.Description != "Test transaction" {
			t.Errorf("Expected description to be 'Test transaction', got %s", transaction.Description)
		}
		if transaction.Amount != 100.50 {
			t.Errorf("Expected amount to be 100.50, got %f", transaction.Amount)
		}
		if transaction.Category != "Food" {
			t.Errorf("Expected category to be 'Food', got %s", transaction.Category)
		}
		if transaction.Type != "expense" {
			t.Errorf("Expected type to be 'expense', got %s", transaction.Type)
		}
	})

	t.Run("should validate transaction type", func(t *testing.T) {
		validTypes := []string{"income", "expense"}

		for _, validType := range validTypes {
			transaction := Transaction{
				Type: validType,
			}

			if transaction.Type != validType {
				t.Errorf("Expected type to be %s, got %s", validType, transaction.Type)
			}
		}
	})
}

func TestInvoice(t *testing.T) {
	t.Run("should create invoice with valid data", func(t *testing.T) {
		now := time.Now()
		dueDate := now.AddDate(0, 0, 30)

		invoice := Invoice{
			ID:            1,
			UserID:        1,
			InvoiceNumber: "INV-001",
			Date:          now,
			DueDate:       dueDate,
			Status:        "draft",
			TaxRate:       0.1,
			Currency:      "USD",
		}

		if invoice.ID != 1 {
			t.Errorf("Expected ID to be 1, got %d", invoice.ID)
		}
		if invoice.UserID != 1 {
			t.Errorf("Expected UserID to be 1, got %d", invoice.UserID)
		}
		if invoice.InvoiceNumber != "INV-001" {
			t.Errorf("Expected invoice number to be 'INV-001', got %s", invoice.InvoiceNumber)
		}
		if invoice.Status != "draft" {
			t.Errorf("Expected status to be 'draft', got %s", invoice.Status)
		}
		if invoice.TaxRate != 0.1 {
			t.Errorf("Expected tax rate to be 0.1, got %f", invoice.TaxRate)
		}
		if invoice.Currency != "USD" {
			t.Errorf("Expected currency to be 'USD', got %s", invoice.Currency)
		}
	})

	t.Run("should validate invoice status", func(t *testing.T) {
		validStatuses := []string{"temporary", "draft", "unpaid", "paid", "overdue"}

		for _, status := range validStatuses {
			invoice := Invoice{
				Status: status,
			}

			if invoice.Status != status {
				t.Errorf("Expected status to be %s, got %s", status, invoice.Status)
			}
		}
	})
}

func TestInvoiceItem(t *testing.T) {
	t.Run("should create invoice item with valid data", func(t *testing.T) {
		item := InvoiceItem{
			ID:          1,
			InvoiceID:   1,
			Description: "Test item",
			Quantity:    2,
			Price:       50.0,
		}

		if item.ID != 1 {
			t.Errorf("Expected ID to be 1, got %d", item.ID)
		}
		if item.InvoiceID != 1 {
			t.Errorf("Expected InvoiceID to be 1, got %d", item.InvoiceID)
		}
		if item.Description != "Test item" {
			t.Errorf("Expected description to be 'Test item', got %s", item.Description)
		}
		if item.Quantity != 2 {
			t.Errorf("Expected quantity to be 2, got %d", item.Quantity)
		}
		if item.Price != 50.0 {
			t.Errorf("Expected price to be 50.0, got %f", item.Price)
		}
	})

	t.Run("should calculate total correctly", func(t *testing.T) {
		item := InvoiceItem{
			Quantity: 3,
			Price:    25.5,
		}

		expectedTotal := float64(item.Quantity) * item.Price
		if expectedTotal != 76.5 {
			t.Errorf("Expected total to be 76.5, got %f", expectedTotal)
		}
	})
}

func TestRecurringTransaction(t *testing.T) {
	t.Run("should create recurring transaction with valid data", func(t *testing.T) {
		now := time.Now()
		recurring := RecurringTransaction{
			ID:          1,
			UserID:      1,
			Description: "Monthly rent",
			Amount:      1000.0,
			Category:    "Housing",
			Type:        "expense",
			Frequency:   "monthly",
			StartDate:   now,
		}

		if recurring.ID != 1 {
			t.Errorf("Expected ID to be 1, got %d", recurring.ID)
		}
		if recurring.UserID != 1 {
			t.Errorf("Expected UserID to be 1, got %d", recurring.UserID)
		}
		if recurring.Description != "Monthly rent" {
			t.Errorf("Expected description to be 'Monthly rent', got %s", recurring.Description)
		}
		if recurring.Amount != 1000.0 {
			t.Errorf("Expected amount to be 1000.0, got %f", recurring.Amount)
		}
		if recurring.Frequency != "monthly" {
			t.Errorf("Expected frequency to be 'monthly', got %s", recurring.Frequency)
		}
		if recurring.StartDate != now {
			t.Errorf("Expected StartDate to be %v, got %v", now, recurring.StartDate)
		}
	})

	t.Run("should validate frequency", func(t *testing.T) {
		validFrequencies := []string{"daily", "weekly", "monthly", "yearly"}

		for _, frequency := range validFrequencies {
			recurring := RecurringTransaction{
				Frequency: frequency,
			}

			if recurring.Frequency != frequency {
				t.Errorf("Expected frequency to be %s, got %s", frequency, recurring.Frequency)
			}
		}
	})
}

func TestBudget(t *testing.T) {
	t.Run("should create budget with valid data", func(t *testing.T) {
		now := time.Now()
		category := "Food"
		budget := Budget{
			ID:          1,
			UserID:      1,
			Category:    &category,
			Amount:      500.0,
			PeriodType:  "monthly",
			PeriodStart: now,
		}

		if budget.ID != 1 {
			t.Errorf("Expected ID to be 1, got %d", budget.ID)
		}
		if budget.UserID != 1 {
			t.Errorf("Expected UserID to be 1, got %d", budget.UserID)
		}
		if budget.Category == nil || *budget.Category != "Food" {
			t.Errorf("Expected category to be 'Food', got %v", budget.Category)
		}
		if budget.Amount != 500.0 {
			t.Errorf("Expected amount to be 500.0, got %f", budget.Amount)
		}
		if budget.PeriodType != "monthly" {
			t.Errorf("Expected period type to be 'monthly', got %s", budget.PeriodType)
		}
		if budget.PeriodStart != now {
			t.Errorf("Expected PeriodStart to be %v, got %v", now, budget.PeriodStart)
		}
	})
}
