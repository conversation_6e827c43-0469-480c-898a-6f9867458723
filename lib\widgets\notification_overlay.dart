import 'package:flutter/material.dart';

class NotificationOverlay {
  static final List<OverlayEntry> _entries = [];

  static void show(BuildContext context, {
    required String message,
    String? title,
    Color backgroundColor = Colors.blue,
    IconData? icon,
    Duration duration = const Duration(seconds: 3),
  }) {
    final overlay = Overlay.of(context);

    late OverlayEntry entry;
    entry = OverlayEntry(
      builder: (context) => _NotificationCard(
        message: message,
        title: title,
        backgroundColor: backgroundColor,
        icon: icon,
        onClose: () {
          entry.remove();
          _entries.remove(entry);
        },
        index: _entries.length,
      ),
    );

    _entries.add(entry);
    overlay.insert(entry);

    Future.delayed(duration, () {
      if (_entries.contains(entry)) {
        entry.remove();
        _entries.remove(entry);
      }
    });
  }
}

class _NotificationCard extends StatelessWidget {
  final String message;
  final String? title;
  final Color backgroundColor;
  final IconData? icon;
  final VoidCallback onClose;
  final int index;

  const _NotificationCard({
    required this.message,
    this.title,
    required this.backgroundColor,
    this.icon,
    required this.onClose,
    required this.index,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      bottom: 24.0 + (index * 70),
      right: 24.0,
      child: Material(
        color: Colors.transparent,
        child: Dismissible(
          key: ValueKey(message + (title ?? '')),
          direction: DismissDirection.endToStart,
          onDismissed: (_) => onClose(),
          child: Container(
            width: 320,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
            decoration: BoxDecoration(
              color: backgroundColor,
              borderRadius: BorderRadius.circular(12),
              boxShadow: const [BoxShadow(color: Colors.black26, blurRadius: 8)],
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (icon != null)
                  Padding(
                    padding: const EdgeInsets.only(top: 2.0, right: 10),
                    child: Icon(icon, color: Colors.white, size: 22),
                  ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (title != null)
                        Text(title!, style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold)),
                      Text(message, style: const TextStyle(color: Colors.white)),
                    ],
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close, color: Colors.white, size: 18),
                  onPressed: onClose,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
} 